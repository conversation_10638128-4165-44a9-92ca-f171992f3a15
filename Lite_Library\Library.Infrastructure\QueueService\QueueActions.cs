﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;

namespace Library.Infrastructure.QueueService;

public class QueueActions
{
    private readonly QueueClient queueClient;

    public QueueActions(QueueSubscription subscription, string connectionString)
    {
        queueClient = new QueueClient(connectionString, subscription.Queuestr, new QueueClientOptions { MessageEncoding = QueueMessageEncoding.Base64 });
    }

    public QueueActions(string subscription, string connectionString)
    {
        queueClient = new QueueClient(connectionString, subscription, new QueueClientOptions { MessageEncoding = QueueMessageEncoding.Base64 });
    }

    public async Task AddToQueue(string data, TimeSpan? visibilityTimeout = null, TimeSpan? timeToLive = null, CancellationToken cancellationToken = default)
    {
        if (!await queueClient.ExistsAsync()) // temporary workaround for internal 409 exception in CreateIfNotExistsAsync
        {
            await queueClient.CreateIfNotExistsAsync();
        }

        await queueClient.SendMessageAsync(data, visibilityTimeout, timeToLive)
            .ConfigureAwait(false);
    }

    public async Task DeleteMessage(QueueMessage[] retrievedMessage)
    {
        await queueClient.DeleteMessageAsync(retrievedMessage[0].MessageId, retrievedMessage[0].PopReceipt)
            .ConfigureAwait(false);
    }

    public async Task<QueueMessage> GetItem()
    {
        QueueMessage[] retrievedMessage = await queueClient.ReceiveMessagesAsync().ConfigureAwait(false);
        return retrievedMessage == null ? default : retrievedMessage.FirstOrDefault();
    }

    public async Task<int> GetMessageCount()
    {
        return (await queueClient.GetPropertiesAsync().ConfigureAwait(false)).Value.ApproximateMessagesCount;
    }
}
