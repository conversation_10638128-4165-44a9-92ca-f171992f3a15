﻿using System;

namespace Library.StorageWriter.Reader_Writer;

public class ClientEmployeeImageBlobReader : BlobReader
{
    public ClientEmployeeImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString,
        "userprofilepicture")
    {
    }
}

public class InvoiceImageBlobReader : BlobReader
{
    public InvoiceImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString,
        "invoiceimages")
    {
    }
}

public class OutletImageBlobReader : BlobReader
{
    public OutletImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString,
        "outletimages")
    {
    }
}

public class AssetImageBlobReader : BlobReader
{
    public AssetImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString,
        "assetimages")
    {
    }
}

public class ProductImageReader : BlobReader
{
    public ProductImageReader(string masterStorageConnectionString) : base(masterStorageConnectionString,
        "productimages")
    {
    }
}

public class SelfiImageBlobReader : BlobReader
{
    public SelfiImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString,
        "daystartimage")
    {
    }

    public string GetSelfiPath(string filename)
    {
        try
        {
            var path = _container.Uri.AbsoluteUri + "/" + filename;
            return !string.IsNullOrWhiteSpace(filename) ? path : null;
        }
        catch (Exception)
        {
            return null;
        }
    }
}
