﻿using Microsoft.Azure.WebJobs;
using RouteOptimizationProcessor.Core.Services;
using RouteOptimizationProcessor.Core.Models.QueueModels;

namespace InputMasterProcessor;

public class InputMasterContinuousProcessor(IInputMasterService inputMasterService)
{
    public async Task ProcessQueueAsync([QueueTrigger("ro-playground-automatic-master-queue", Connection = "StorageConnectionString")] RoutePlaygroundQueueModel queueData)
    {
        try
        {
            await inputMasterService.ProcessQueueAsync(queueData);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            throw;
        }
    }
}
