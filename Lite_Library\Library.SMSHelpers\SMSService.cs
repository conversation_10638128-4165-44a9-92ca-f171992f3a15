﻿using System;
using System.Threading.Tasks;
using Library.Infrastructure.QueueService;

namespace Library.SMSHelpers;

public class SMSSender
{
    private readonly QueueHandler<SMSMessage> smsClient;

    public SMSSender(string masterStorageConnStrin)
    {
        smsClient = new QueueHandler<SMSMessage>(QueueType.SendSMS, masterStorageConnStrin);
    }

    public async Task Send(string to, string body)
    {
        var message = new SMSMessage { Message = body, To = to };
        await Send(message).ConfigureAwait(false);
    }

    public async Task Send(SMSMessage smsMessage)
    {
        var refId = Guid.NewGuid().ToString();
        await smsClient.AddToGridQueue(refId, smsMessage).ConfigureAwait(false);
    }
}
