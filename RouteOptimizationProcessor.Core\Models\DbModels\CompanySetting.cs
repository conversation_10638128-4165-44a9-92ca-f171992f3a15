﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace RouteOptimizationProcessor.Core.Models.DbModels;

[Table("CompanySettings")]
public class CompanySetting
{
    public long Id { get; set; }

    public string? SettingKey { get; set; }

    public CompanySettingType SettingType { get; set; }

    public bool SendInApp { get; set; }

    public string? DefaultValue { get; set; }

    public string? MinVersionSupported { get; set; }

    public bool IsDeprecated { get; set; }
}

public class CompanySettingValue
{
    public long Id { get; set; }

    public long SettingId { get; set; }

    public long CompanyId { get; set; }

    public string? SettingValue { get; set; }
}
