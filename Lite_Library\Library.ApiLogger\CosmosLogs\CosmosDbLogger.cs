﻿using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Library.ApiLogger.Interface;
using Microsoft.Azure.Cosmos;

namespace Library.ApiLogger.CosmosLogs;

public class CustomCosmosSerializer : CosmosSerializer
{
    public override T FromStream<T>(Stream stream)
    {
        var data = JsonSerializer.Deserialize<T>(stream);
        stream.Dispose();
        return data;
    }

    public override Stream ToStream<T>(T input)
    {
        return new MemoryStream(JsonSerializer.SerializeToUtf8Bytes(input));
    }
}

public class CosmosDbLogger : IPrimaryLogWriter
{
    private readonly ILogWriter _logWriter;
    private readonly CosmosClient client;
    private readonly CosmosSource cosmosSource;

    public CosmosDbLogger(CosmosSource cosmosSource, ISecondaryLogWriter logWriter)
    {
        this.cosmosSource = cosmosSource;
        _logWriter = logWriter;
        client = new CosmosClient(cosmosSource.EndpointUrl, cosmosSource.AuthorizationKey, new CosmosClientOptions { Serializer = new CustomCosmosSerializer() });
    }

    public async Task Log(IApiLog request)
    {
        var t = _logWriter.Log(request).ConfigureAwait(false);
        await CreateDocument(request).ConfigureAwait(false);
        await t;
    }

    private async Task CreateDocument(IApiLog log)
    {
        if (log.CompanyId == 0)
        {
            Console.WriteLine("⚠️ Company ID is 0!");
            return;
        }

        log.Input = log.Input.Length.ToString();
        log.Output = log.Output.Length.ToString();

        if (string.IsNullOrWhiteSpace(log.PartitionKey))
        {
            log.PartitionKey = $"{log.CompanyId}-{DateTime.Now.ToString("yyyyMMdd")}";
        }

        try
        {
            var container = client.GetContainer(cosmosSource.DatabaseId, cosmosSource.CollectionId);
            await container.CreateItemAsync((ApiLogRequest)log).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            await Console.Error.WriteAsync($"CompanyId : {log.CompanyId}, RequestId : {log.RequestId}, Error: {ex.GetBaseException().Message}").ConfigureAwait(false);
        }
    }
}
