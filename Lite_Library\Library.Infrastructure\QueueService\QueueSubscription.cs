﻿using System.Collections.Generic;

namespace Library.Infrastructure.QueueService;

public enum QueueType
{
    SendEmail,
    SendSMS,
    SlackAutoBug,
    SlackGeneric,
    UpdateEvent,
    UpdateDemoCompany,
    UpdateEventPoison,
    StaleDataTrigger,
    StaleDataTriggerRetry,
    DSRQueue,
    SendEmbeddedEmail,
    SlackCloudLogs,
    SendOtp,
    LiveProcessorEventQueue,
    DeleteUserStatus,
    DeleteMappingsQueueName,
    MTResetSales,
    SendEmailOTP,
    QPSSecondaryQueue,
    EmailDeliveryStatus,
    GcmNotification,
    FcmNotification,
    UpdateDispatchQueueName,
    UpdateDispatchQueueNameNew,
    UpdateNewOutletCountQueueName,
    QueueNameSpecial,
    V4ReportRequestQueue,
    V3ReportRequestQueue,
    V4ReportLiveTransactionReportQueue,
    V4FlexibleRequestQueue,
    V4MasterRequestQueue,
    OutletClusterQueue,
    AssetManagementQueue,
    FloSyncEmployeeQueue,
    MTReportQueue,
    TaskManagementQueue,
    Dsr,
    MonthlyDsr,
    Beatometer,
    TradeAppQueue,
    ETimsManagementQueue,
    LocationAdditionWithCallQueue,
    VanSalesDaySessionQueue,
    FloMasterSyncQueue,
    FloMasterSyncQueueContinous,
    FlexibleTargetQueue,
    V4FlexibleTargetAchReportQueue,

    #region RoQueues

    RoEmployeeQueue,
    RoOutletQueue,
    RoOutletClusterQueue,
    RoEmployeeRoutesQueue,
    RoEmployeeRoutesTspQueue,
    RouteIntegrationQueue,
    RoWeeklyEmployeeRoutesQueue,
    RoEmployeeRoutesTspLsQueue,
    RoWeeklyEmployeeQueue,
    SuggestiveRouteQueue,
    #endregion,
    #region Route Playground
    RoPlaygroundFileSplitQueue,
    RoPlaygroundAutomaticMasterQueue,
    RoPlaygroundUpdationQueue,
    #endregion
    ApiLogs,

    #region EngageQueues

    EngageLogs,
    EngageMessageNotification,

    #endregion

    #region Perfect Store Queues

    PerfectStoreTaskQueue,
    PerfectStoreTagQueue,
    PerfectStoreFocusAreaQueue,
    PerfectEntityRuleQueue,
    #endregion

    #region TargetAutomationQueues

    TsMonthyToDailyQueue,
    TsCompanyTargetSubsQueue,
    TsDailyTargetToMasterQueue,
    TsDeltaAddtionQueue,

    #endregion

    #region NewApprovalMechanism

    NewApprovalMechanismTimeline,
    NewApprovalAddUpdateQueue,

    #endregion

    #region ClickHouse Presepectives

    UserActivityQueue,
    UserActivityHistoricalQueue,

    #endregion

    #region PMS Queues

    PmsDailyQueue,

    #endregion
}

public class QueueSubscription
{
    private static readonly Dictionary<QueueType, string> queueForEvents = new()
    {
        { QueueType.SendEmail, "email-queue" },
        { QueueType.SendSMS, "sms-queue" },
        { QueueType.SlackAutoBug, "slack-autobug-queue" },
        { QueueType.SlackGeneric, "slack-queue" },
        { QueueType.UpdateEvent, "updateevent-queue" },
        { QueueType.UpdateDemoCompany, "fakeeventqueue" },
        { QueueType.UpdateEventPoison, "updateevent-queue-poison" },
        { QueueType.StaleDataTrigger, "staledata-queue" },
        { QueueType.StaleDataTriggerRetry, "staledata-queue-retry" },
        { QueueType.EmailDeliveryStatus, "email-delivery-status" },
        { QueueType.GcmNotification, "app-notification-queue" },
        { QueueType.FcmNotification, "fcm-notification-queue" },
        { QueueType.SendOtp, "email-queue-otp" },
        { QueueType.LiveProcessorEventQueue, "eventqueue" },
        { QueueType.DeleteUserStatus, "deleteuserstatus" },
        { QueueType.DeleteMappingsQueueName, "mt-deletemapping-queue" },
        { QueueType.MTResetSales, "mtresetsales" },
        { QueueType.SendEmailOTP, "email-queue-otp" },
        { QueueType.QPSSecondaryQueue, "qpsoutletqueue" },
        { QueueType.SendEmbeddedEmail, "email-embedded-queue" },
        { QueueType.SlackCloudLogs, "slack-queue-facloudlogs" },
        { QueueType.DSRQueue, "dsrrequest-queue" },
        { QueueType.V4ReportRequestQueue, "ns-reportrequest-queue" },
        { QueueType.V3ReportRequestQueue, "v3-reportrequest-queue" },
        { QueueType.V4ReportLiveTransactionReportQueue, "ns-reportrequest-live-queue" },
        { QueueType.V4FlexibleRequestQueue, "ns-flexiblereportrequest-queue" },
        { QueueType.V4MasterRequestQueue, "ns-masterreportrequest-queue" },
        { QueueType.OutletClusterQueue, "outletclusterqueue" },
        { QueueType.AssetManagementQueue, "asset-management-queue" },
        { QueueType.UpdateDispatchQueueName, "dispatch-queue" },
        { QueueType.UpdateDispatchQueueNameNew, "dispatch-queue-rp" },
        { QueueType.UpdateNewOutletCountQueueName, "newoutletrequestcount-queue" },
        { QueueType.QueueNameSpecial, "ns-reportrequest-queue-special" },
        { QueueType.FloSyncEmployeeQueue, "wf-employeesync-queue" },
        { QueueType.MTReportQueue, "mt-reportrequest-queue" },
        { QueueType.TaskManagementQueue, "task-management-queue" },
        { QueueType.Dsr, "ns-dsr-queue" },
        { QueueType.MonthlyDsr, "ns-mdsr-queue" },
        { QueueType.Beatometer, "beatometer-queue" },
        { QueueType.TradeAppQueue, "tradeapp-fcm-notification-queue" },
        { QueueType.ETimsManagementQueue, "etims-management-queue" },
        { QueueType.VanSalesDaySessionQueue, "vansales-daysession-queue" },
        { QueueType.LocationAdditionWithCallQueue, "locationadditionwithcall-eventqueue" },
        { QueueType.FloMasterSyncQueue, "sfatoflomastersync-queue" },
        { QueueType.FloMasterSyncQueueContinous, "sfatoflomastersync-continuous-queue" },
        { QueueType.FlexibleTargetQueue, "flexible-target-queue" },
        { QueueType.V4FlexibleTargetAchReportQueue, "ns-flexibletargetachrequest-queue" },

        #region RoQueues

        { QueueType.RoEmployeeQueue, "ro-employee-queue" },
        { QueueType.RoOutletQueue, "ro-outlet-queue" },
        { QueueType.RoOutletClusterQueue, "ro-outlet-cluster-queue" },
        { QueueType.RoEmployeeRoutesQueue, "ro-employee-routes-queue" },
        { QueueType.RoEmployeeRoutesTspQueue, "ro-employee-routes-tsp-queue" },
        { QueueType.RouteIntegrationQueue, "route-integration-queue" },
        { QueueType.RoWeeklyEmployeeRoutesQueue, "ro-employee-weekly-routes-queue" },
        { QueueType.RoEmployeeRoutesTspLsQueue, "ro-employee-tsp-ls-queue" },
        { QueueType.RoWeeklyEmployeeQueue, "ro-weekly-employee-queue" },
        { QueueType.SuggestiveRouteQueue, "suggestive-route-queue"},
        #endregion

        #region RoPlaygroundQueues
        { QueueType.RoPlaygroundFileSplitQueue, "ro-playground-file-split-queue"},
        { QueueType.RoPlaygroundAutomaticMasterQueue, "ro-playground-automatic-master-queue"},
        { QueueType.RoPlaygroundUpdationQueue, "ro-playground-updation-queue"},
        #endregion

        { QueueType.ApiLogs, "api-logs" },

        #region EngageQueues

        { QueueType.EngageLogs, "engage-logs" },
        { QueueType.EngageMessageNotification, "engagemessagenotificationqueue" },

        #endregion

        #region NewApprovalMechanism

        { QueueType.NewApprovalMechanismTimeline, "newapprovalmechanism-timeline-queue" },
        { QueueType.NewApprovalAddUpdateQueue, "newapprovalmechanism-addupdate-queue" },

        #endregion

        #region ClickHouse Presepectives Queues

        { QueueType.UserActivityQueue, "user-activity" },
        { QueueType.UserActivityHistoricalQueue, "user-activity-historical" },

        #endregion

        #region Perfect Store Queue

        { QueueType.PerfectStoreTaskQueue, "perfectstore-task-queue" },
        { QueueType.PerfectStoreTagQueue, "perfectstore-tag-queue" },
        { QueueType.PerfectStoreFocusAreaQueue, "perfectstore-focusarea-queue" },
        {QueueType.PerfectEntityRuleQueue, "perfectentityrule-queue"},
        #endregion

        #region TargetAutomation Queues

        { QueueType.TsMonthyToDailyQueue, "ts-monthytodaily-queue" },
        { QueueType.TsCompanyTargetSubsQueue, "ts-companytargetsubs-queue" },
        { QueueType.TsDailyTargetToMasterQueue, "ts-dailytargetmaster-queue" },
        { QueueType.TsDeltaAddtionQueue, "ts-deltaaddition-queue" },

        #endregion

        #region PMS Queues

        { QueueType.PmsDailyQueue, "pms-daily-queue" },

        #endregion
    };

    public QueueSubscription(QueueType queueType)
    {
        Queuestr = queueForEvents[queueType];
        QueueType = queueType;
    }

    public string Queuestr { get; }
    public QueueType QueueType { get; }
}
