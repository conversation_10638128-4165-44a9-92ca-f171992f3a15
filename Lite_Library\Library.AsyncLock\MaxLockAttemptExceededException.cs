﻿using System;
using System.Runtime.Serialization;

namespace Library.AsyncLock;

[Serializable]
public class AcquireLockException : Exception
{
    protected AcquireLockException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public AcquireLockException()
    {
    }

    public AcquireLockException(string message) : base(message)
    {
    }

    public AcquireLockException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

[Serializable]
public class MaxLockAttemptExceededException : Exception
{
    protected MaxLockAttemptExceededException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public MaxLockAttemptExceededException()
    {
    }

    public MaxLockAttemptExceededException(string message) : base(message)
    {
    }

    public MaxLockAttemptExceededException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

[Serializable]
public class ReleaseLockException : Exception
{
    protected ReleaseLockException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public ReleaseLockException()
    {
    }

    public ReleaseLockException(string message) : base(message)
    {
    }

    public ReleaseLockException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
