﻿using System.ComponentModel;

namespace Libraries.CommonEnums;

public enum CampaignBasis
{
    PC = 0,
    SC = 1,
    Product = 2,
    Outlet = 3
}

public enum CampaignFrequency
{
    [Description("Every Visit")]
    EveryVisit = 0,

    [Description("Once Every Week")]
    OnceEveryWeek = 1,

    [Description("Once Every Month")]
    OnceEveryMonth = 2,

    [Description("Defined Day Of Week")]
    DefinedDayOfWeek = 3
}

public enum CampaignTaskStatus
{
    Pending,
    Completed
}

public enum CampaignType
{
    [Description("SKU Availability")]
    SKU_Availability,

    [Description("Planogram Compliance")]
    Planogram_Compliance,

    [Description("Shelf Share")]
    Shelf_Share,

    Other,

    [Description("Promo")]
    Promo,

    [Description("Competitor Survey")]
    Competitor_Survey,

    [Description("IR Campaign")]
    IR_Campaign
}

public enum IRCampaignType
{
    FAIR = 0,
    PartnerIR = 1
}

public enum IRSelectKPIs
{
    ProductAvailability = 0,
    SOS = 1
}

public enum IRDisplayTypes
{
    Fridge = 0,
    Cooler = 1,
    Shelf = 2
}

public enum CampaignWeek
{
    Sun = 0,
    Mon = 1,
    Tue = 2,
    Wed = 3,
    Thur = 4,
    Fri = 5,
    Sat = 6
}

public enum CampaignImageAuditStatus
{
    Pending,
    Approved,
    Rejected
}

public enum CampaignBreachStatus
{
    Pending,
    Approved
}

public enum CampaignBreachVerificationContactType
{
    User,
    Manager,
    Outlet,
    Distributor
}
