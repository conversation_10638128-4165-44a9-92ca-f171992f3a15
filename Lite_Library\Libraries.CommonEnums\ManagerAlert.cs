﻿namespace Libraries.CommonEnums;

public enum ManagerAlertAction
{
    Pending,
    Approved,
    Disapproved,
    Archived
}

public enum AlertType
{
    StarKRA = 0,
    TourPlanCreation = 1,
    OutletUpdation = 2,
    LateDayStart = 3,
    LateDayEnd = 4,
    RoutePlanRequest = 5,
    NewOutletCreationRequest = 6,
    OTP = 7,
    OTPNSApp = 8,
    OpportunityOutletRequest = 9,
    ActionCardResponseApprovalRequest = 10,
    OutletVerification = 11,
    TADAApprovals = 12,
    DeadOutletRequest = 13,
    NotificationSummary = 14,
    RegulariseAttendance = 15,
    LeaveHR = 16,
    OutletRouteAdditionRequest = 1000,
    VanDayLoadOut = 18,
    SuggestedOrder = 19,
    DistributorStock = 17,
    FaEngage = 20,

    //MTAlertType
    StockOut = 101,
    ISRStockEditRequest = 102,
    ISRTertiarySalesEditRequest = 103,
    ISRStockInwardRequest = 104,
    ShelfShareTargetBreach = 105,
    PurchaseOrderApprovalRequest = 106,
    AttendanceRegularise = 107,
    UnaccountedStockEditRequest = 108,
    MTLeaveHR = 109,
    MTCampaignBreach = 110,
    AssetReallocationRequest = 30,
    VanDayLoadIn = 40,
    FaEngageNotAggregated = 53
}

public enum OTPRequestType
{
    JourneyDiversion,
    OrderValidation
}
