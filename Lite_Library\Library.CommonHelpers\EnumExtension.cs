﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Library.CommonHelpers;

public static class EnumExtension
{
    public static string GetDescription(Enum value)
    {
        var fi = value.GetType().GetField(value.ToString());

        return fi.GetCustomAttributes(typeof(DescriptionAttribute), false) is DescriptionAttribute[] attributes && attributes.Any() ? attributes.First().Description : value.ToString();
    }

    public static Dictionary<int, string> GetDictionary(Type t, bool useDisplayNameIfAvailable = true)
    {
        return !t.IsEnum
            ? new Dictionary<int, string>()
            : Enum.GetValues(t)
                .Cast<Enum>()
                .ToDictionary(v => Convert.ToInt32(v),
                    v => useDisplayNameIfAvailable ? v.GetDisplayName() : v.ToString());
    }

    public static string GetDisplayName(this Enum seg)
    {
        var enumField = seg.GetType()
            .GetField(seg.ToString());
        var display = enumField?.GetCustomAttributes(false)
            .OfType<DisplayAttribute>()
            .SingleOrDefault();
        return display == null ? seg.ToString() : display.Name;
    }

    public static string GetSqlDropFunctionForEnum(Type item, string funcName = null)
    {
        funcName ??= $"Get{item.Name}String";
        var sqlFunction = "";
        if (item.IsEnum)
        {
            sqlFunction += $@"IF OBJECT_ID (N'{funcName}', N'FN') IS NOT NULL
DROP FUNCTION {funcName};

Go";
        }

        return sqlFunction;
    }

    public static string GetSqlFunctionForEnum(Type item, string funcName = null,
        bool useDisplayNameIfAvailable = true)
    {
        funcName ??= $"Get{item.Name}String";
        var sqlFunction = "";
        if (item.IsEnum)
        {
            sqlFunction += GetSqlDropFunctionForEnum(item, funcName);
            sqlFunction += $@"

Create Function {funcName}(@enumVal int)
    RETURNS varchar(50)
    AS
    --
Begin
    return (case @enumVal ";
            var nameList = GetDictionary(item, useDisplayNameIfAvailable);
            foreach (var enumitem in nameList)
            {
                sqlFunction += $@"when {enumitem.Key} then '{enumitem.Value}'
";
            }

            sqlFunction += @"else '?????'
end)
end;

Go";
        }

        return sqlFunction;
    }

    public static List<string> GetValidList<T>(List<int> ignoredItems = null) where T : struct, IConvertible
    {
        ignoredItems ??= new List<int>();
        return GetDictionary(typeof(T)).Where(s => !ignoredItems.Contains(s.Key)).Select(x => x.Value).ToList();
    }

    public static List<string> GetValidList(Type type, List<int> ignoredItems)
    {
        return !type.IsEnum
            ? new List<string>()
            : Enum.GetValues(type)
                .Cast<Enum>()
                .Select(v => v.GetDisplayName())
                .ToList();
    }

    public static T ParseEnumValue<T>(string value)
    {
        foreach (var field in typeof(T).GetFields())
        {
            if (Attribute.GetCustomAttribute(field, typeof(DisplayAttribute)) is DisplayAttribute displayAttribute)
            {
                if (displayAttribute.Name.Equals(value, StringComparison.OrdinalIgnoreCase))
                {
                    return (T)field.GetValue(null);
                }
            }
        }

        throw new ArgumentException($"No enum constant with display name '{value}' found in enum {typeof(T).Name}");
    }
}
