<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.KeyVault" Version="1.0.6" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions" Version="5.0.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="5.3.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage.Queues" Version="5.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Sentry" Version="5.5.1" />
    <PackageReference Include="Serilog" Version="4.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Sentry.AspNetCore" Version="5.5.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Lite_Library\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.StorageWriter\Library.StorageWriter.csproj" />
    <ProjectReference Include="..\RouteOptimizationProcessor.Core\RouteOptimizationProcessor.Core.csproj" />
    <ProjectReference Include="..\RouteOptimizationProcessor.DbStorage\RouteOptimizationProcessor.DbStorage.csproj" />
  </ItemGroup>

</Project>
