﻿using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Fluent;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Libraries.CosmosV3.CosmosDb;

public abstract class CosmosDbBase
{
    protected Database db;

    protected CosmosDbBase(string connectionString, string dbName)
    {
        var client = new CosmosClientBuilder(connectionString)
            .WithCustomSerializer(new CosmosJsonCustomSerializer(new JsonSerializerSettings
            {
                DateParseHandling = DateParseHandling.DateTimeOffset, DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind, ContractResolver = new CamelCasePropertyNamesContractResolver()
            }))
            .Build();
        db = client.GetDatabase(dbName);
    }

    public Container GetContainer(string collection)
    {
        return db.GetContainer(collection);
    }
}
