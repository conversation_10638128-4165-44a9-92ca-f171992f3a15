using Microsoft.Azure.WebJobs;
using RouteOptimizationProcessor.Core.Models.QueueModels;
using RouteOptimizationProcessor.Core.Services;

namespace RouteUploadProcessor;
public class PlaygroundRouteUploadProcessor(IUploadService uploadService)
{
    public async Task ProcessQueueAsync([QueueTrigger("ro-playground-route-integration-queue", Connection = "StorageConnectionString")]
            PlayGroundRouteUploadQueueModel queueData)
    {
        try
        {
            var ct = new CancellationToken();
            await uploadService.UploadRoutes(queueData, ct);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            throw;
        }
    }
}

