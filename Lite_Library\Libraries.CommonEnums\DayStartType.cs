using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum DayStartType
{
    [Display(Name = "Retailing")]
    Regular = 0,

    [Display(Name = "Other")]
    Other = 1,

    [Display(Name = "Leave")]
    Leave = 2,

    [Display(Name = "Holiday")]
    Holiday = 3,

    [Display(Name = "Weekly Off")]
    WeeklyOff = 4,

    [Display(Name = "Official Work")]
    OfficialWork = 5,

    [Display(Name = "NonRetailing Manager Work")]
    NonRetailingManagerWork = 6,

    [Display(Name = "Manager Joint Working")]
    ManagerJointWorking = 7,

    [Display(Name = "Telecaller Day Start")]
    TelecallerDayStart = 8,

    [Display(Name = "Retailer Application Day Start")]
    RetailerApplicationDayStart = 9,

    [Display(Name = "Manager Working")]
    ManagerWorking = 10,

    [Display(Name = "Spoke Working")]
    SpokeWorking = 11,

    [Display(Name = "Activity Under Different Manager")]
    HierarchyVariability = 98,

    [Display(Name = "Absent")]
    None = 99
}

public enum SupervisorDayStartType
{
    [Display(Name = "Merchandising")]
    Regular = 0,

    [Display(Name = "Other")]
    Other = 1,

    [Display(Name = "Leave")]
    Leave = 2,

    [Display(Name = "Holiday")]
    Holiday = 3,

    [Display(Name = "WeeklyOff")]
    WeeklyOff = 4,

    [Display(Name = "Official Work")]
    OfficialWork = 5,

    [Display(Name = "NonRetailing Manager Work")]
    NonRetailingManagerWork = 6,

    [Display(Name = "Manager Joint Working")]
    ManagerJointWorking = 7,

    [Display(Name = "Telecaller Day Start")]
    TelecallerDayStart = 8,

    [Display(Name = "Manager Working")]
    ManagerWorking = 10,

    None = 99
}

public enum DayStartTypeCategory
{
    Retailing,
    OfficialWork,
    Leave,
    Absent,
    ManagerJW,
    WeeklyOff,
    Holiday,
    ManagerWorking
}

public static class DayStartReasonCategory
{
    /// <summary>
    /// Mapping of DayStartType to DayStartTypeCategory
    /// </summary>
    private static readonly Dictionary<string, DayStartType> _dayStartTypeMappings = new()
    {
        { "Retailing", DayStartType.Regular },
        { "Leave", DayStartType.Leave },
        { "Holiday", DayStartType.Holiday },
        { "WeeklyOff", DayStartType.WeeklyOff },
        { "Absent", DayStartType.None },
        { "ManagerJointWorking", DayStartType.ManagerJointWorking },
        { "NonRetailingManagerWork", DayStartType.NonRetailingManagerWork },
        { "Other", DayStartType.Other },
        { "Weekly Off", DayStartType.WeeklyOff },
        { "ManagerWorking", DayStartType.ManagerWorking },
        { "RetailerApplicationDayStart", DayStartType.RetailerApplicationDayStart },
        { "SpokeWorking", DayStartType.SpokeWorking }
    };

    public static DayStartType GetDayStartType(string type)
    {
        if (string.IsNullOrEmpty(type))
        {
            return DayStartType.OfficialWork;
        }

        return _dayStartTypeMappings.GetValueOrDefault(type, DayStartType.OfficialWork);
    }
}
