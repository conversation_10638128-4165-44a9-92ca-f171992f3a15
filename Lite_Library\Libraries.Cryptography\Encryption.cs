﻿using System.Security.Cryptography;
using System.Text;

namespace Libraries.Cryptography;

public class Encryptor
{
    public static string CalculateMD5Hash(string input, string salt = "")
    {
        // step 1, calculate MD5 hash from input
        var md5 = MD5.Create();
        var inputBytes = Encoding.ASCII.GetBytes(input + salt);
        var hash = md5.ComputeHash(inputBytes);
        // step 2, convert byte array to hex string
        var sb = new StringBuilder();
        for (var i = 0; i < hash.Length; i++)
        {
            sb.Append(hash[i].ToString("X2"));
        }

        return sb.ToString();
    }
}
