﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class LocationRepository(MasterDbContext masterDbContext) : ILocationRepository
{
    public async Task<List<Location>> GetOutletsFromRoutesAsync(List<long> routes, long companyId)
    {
        var locationWithRoutes = await masterDbContext.RouteOutletMappings.Where(d => routes.Contains(d.RouteId) && !d.Deleted)
        .GroupBy(d => d.LocationId).Select(d => new
        {
            LocationId = d.Key
        }
        ).ToListAsync();
        var locationIds = locationWithRoutes.Select(d => d.LocationId).Distinct().ToList();
        return await (
               from l in masterDbContext.Locations
               where l.CompanyId == companyId && !l.IsBlocked && locationIds.Contains(l.Id)
               join st in masterDbContext.ShopTypes on l.ShopTypeId equals st.Id into stGroup
               from st in stGroup.DefaultIfEmpty()
               join ch in masterDbContext.Channels on st.ChannelId equals ch.Id into chGroup
               from ch in chGroup.DefaultIfEmpty()
               select new Location
               {
                   Id = l.Id,
                   CompanyId = l.CompanyId,
                   BeatId = l.BeatId,
                   ShopTypeId = l.ShopTypeId,
                   ChannelId = ch.Id,
                   ShopName = l.ShopName,
                   ShopTypeCode = l.ShopTypeCode,
                   Latitude = l.Latitude,
                   Longitude = l.Longitude,
                   ErpId = l.ErpId,
                   IsBlocked = l.IsBlocked,
                   IsFocused = l.IsFocused,
                   CompanySegmentation = l.CompanySegmentation,
                   Segmentation = l.Segmentation
               }
           ).ToListAsync();

    }
    public async Task<List<Location>> GetOutletsFromBeatsAsync(long companyId, List<long> beatIds)
    {
        if (beatIds == null || beatIds.Count == 0)
        {
            return [];
        }

        var query = await (
            from l in masterDbContext.Locations
            where l.CompanyId == companyId && !l.IsBlocked && beatIds.Contains(l.BeatId)
            join st in masterDbContext.ShopTypes on l.ShopTypeId equals st.Id into stGroup
            from st in stGroup.DefaultIfEmpty()
            join ch in masterDbContext.Channels on st.ChannelId equals ch.Id into chGroup
            from ch in chGroup.DefaultIfEmpty()
            select new Location
            {
                Id = l.Id,
                CompanyId = l.CompanyId,
                BeatId = l.BeatId,
                ShopTypeId = l.ShopTypeId,
                ChannelId = ch.Id,
                ShopName = l.ShopName,
                ShopTypeCode = l.ShopTypeCode,
                Latitude = l.Latitude,
                Longitude = l.Longitude,
                ErpId = l.ErpId,
                IsBlocked = l.IsBlocked,
                IsFocused = l.IsFocused,
                CompanySegmentation = l.CompanySegmentation,
                Segmentation = l.Segmentation
            }
        ).ToListAsync();

        return query;
    }


    public async Task<Dictionary<OutletSegmentation, List<long>>> GetSegementionEnumsForIds(List<long> segmentationIds, long companyId)
    {
        return await masterDbContext.OutletSegmentationAttributes
            .Where(s => s.CompanyId == companyId && segmentationIds.Contains(s.Id))
            .GroupBy(s => s.Segmentation)
            .ToDictionaryAsync(gr => gr.Key, gr => gr.Select(s => s.Id).ToList());
    }

    public async Task<Dictionary<string, long>> GetLocationErpDictionary(long companyId, List<string> locationErpIds, CancellationToken ct)
    {
        return (await masterDbContext.Locations.Where(l => l.CompanyId == companyId && !l.IsBlocked && l.ErpId != null && locationErpIds.Contains(l.ErpId))
            .Select(l => new { ErpId = l.ErpId!, l.Id }).ToListAsync(ct))
            .GroupBy(l => l.ErpId).ToDictionary(l => l.Key, l => l.First().Id);
    }
}

