﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class LocationRepository(MasterDbContext masterDbContext) : ILocationRepository
{
    public async Task<List<Location>> GetOutletsFromRoutesAsync(List<long> routes, long companyId)
    {
        var locationWithRoutes = await masterDbContext.RouteOutletMappings.Where(d => routes.Contains(d.RouteId) && !d.Deleted)
        .GroupBy(d => d.LocationId).Select(d => new
        {
            LocationId = d.Key
        }
        ).ToListAsync();
        var locationIds = locationWithRoutes.Select(d => d.LocationId).Distinct().ToList();
        return await (
            from f in masterDbContext.Locations
            join st in masterDbContext.ShopTypes on f.ShopTypeId equals st.Id
            join ch in masterDbContext.Channels on st.ChannelId equals ch.Id
            where locationIds.Contains(f.Id)
                && f.CompanyId == companyId
                && !f.IsBlocked
            select new Location
            {
                AttributeBoolean1 = f.AttributeBoolean1,
                AttributeBoolean2 = f.AttributeBoolean2,
                AttributeDate1 = f.AttributeDate1,
                AttributeDate2 = f.AttributeDate2,
                AttributeNumber1 = f.AttributeNumber1,
                AttributeNumber2 = f.AttributeNumber2,
                AttributeNumber3 = f.AttributeNumber3,
                AttributeNumber4 = f.AttributeNumber4,
                AttributeText1 = f.AttributeText1,
                AttributeText2 = f.AttributeText2,
                AttributeText3 = f.AttributeText3,
                AttributeText4 = f.AttributeText4,
                BeatId = f.BeatId,
                City = f.City,
                CompanyId = f.CompanyId,
                CompanySegmentation = f.CompanySegmentation,
                Country = f.Country,
                CustomTags = f.CustomTags,
                District = f.District,
                ErpId = f.ErpId,
                Id = f.Id,
                IsBlocked = f.IsBlocked,
                IsFocused = f.IsFocused,
                Latitude = f.Latitude,
                Longitude = f.Longitude,
                MarketName = f.MarketName,
                ShopName = f.ShopName,
                Segmentation = f.Segmentation,
                ShopTypeCode = f.ShopTypeCode,
                State = f.State,
                SubCity = f.SubCity,
                ShopTypeId = f.ShopTypeId,
                ChannelId = ch.Id,
            }
        ).ToListAsync();
    }

    public async Task<List<Location>> GetOutletsFromBeatsAsync(long companyId, List<long> beatIds)
    {
        return await (
            from l in masterDbContext.Locations
            join st in masterDbContext.ShopTypes on l.ShopTypeId equals st.Id
            join ch in masterDbContext.Channels on st.ChannelId equals ch.Id
            where l.CompanyId == companyId
                && !l.IsBlocked
                && beatIds.Contains(l.BeatId)
            select new Location
            {
                AttributeBoolean1 = l.AttributeBoolean1,
                AttributeBoolean2 = l.AttributeBoolean2,
                AttributeDate1 = l.AttributeDate1,
                AttributeDate2 = l.AttributeDate2,
                AttributeNumber1 = l.AttributeNumber1,
                AttributeNumber2 = l.AttributeNumber2,
                AttributeNumber3 = l.AttributeNumber3,
                AttributeNumber4 = l.AttributeNumber4,
                AttributeText1 = l.AttributeText1,
                AttributeText2 = l.AttributeText2,
                AttributeText3 = l.AttributeText3,
                AttributeText4 = l.AttributeText4,
                BeatId = l.BeatId,
                City = l.City,
                CompanyId = l.CompanyId,
                CompanySegmentation = l.CompanySegmentation,
                Country = l.Country,
                CustomTags = l.CustomTags,
                District = l.District,
                ErpId = l.ErpId,
                Id = l.Id,
                IsBlocked = l.IsBlocked,
                IsFocused = l.IsFocused,
                Latitude = l.Latitude,
                Longitude = l.Longitude,
                MarketName = l.MarketName,
                ShopName = l.ShopName,
                Segmentation = l.Segmentation,
                ShopTypeCode = l.ShopTypeCode,
                State = l.State,
                SubCity = l.SubCity,
                ShopTypeId = l.ShopTypeId,
                ChannelId = ch.Id,
            }
        ).ToListAsync();


    }

    public async Task<Dictionary<OutletSegmentation, List<long>>> GetSegementionEnumsForIds(List<long> segmentationIds, long companyId)
    {
        return await masterDbContext.OutletSegmentationAttributes
            .Where(s => s.CompanyId == companyId && segmentationIds.Contains(s.Id))
            .GroupBy(s => s.Segmentation)
            .ToDictionaryAsync(gr => gr.Key, gr => gr.Select(s => s.Id).ToList());
    }
}

