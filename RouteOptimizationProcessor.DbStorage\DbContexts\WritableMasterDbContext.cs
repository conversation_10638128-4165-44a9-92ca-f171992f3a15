﻿using EntityHelper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using RouteOptimizationProcessor.Core.Models.DbModels;

namespace RouteOptimizationProcessor.DbStorage.DbContexts;

public class WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options) : DbContext(options)
{
    public DbSet<Route> Routes { get; set; }
    public DbSet<RouteOutletMapping> RouteOutletMappings { get; set; }
    public DbSet<RoutePlan> RoutePlans { get; set; }
    public DbSet<RoutePlanItem> RoutePlanItems { get; set; }
    public DbSet<RouteAutomationConfiguration> RouteAutomationConfigurations { get; set; }
    public DbSet<RoutePositionMapping> RoutePositionMappings { get; set; }
    public DbSet<EmployeeRouteMapping> FAEmployeeRouteMappings { get; set; }
    public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }
    public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }
    public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }

    public override int SaveChanges()
    {
        throw new InvalidOperationException("use save changes async instead");
    }

    public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
    {
        var newAuditedEntities = ChangeTracker.Entries<IAuditedEntity>()
           .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified).Select(e => e.Entity).ToList();
        var now = DateTime.UtcNow;
        foreach (var item in newAuditedEntities)
        {
            item.CreatedAt = now;
            item.LastUpdatedAt = now;
            item.CreationContext = "From RO PlayGround Processor";
        }
        var createdEntities = ChangeTracker.Entries<ICreatedEntity>()
             .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
        foreach (var item in createdEntities)
        {
            item.CreatedAt = now;
            item.CreationContext = "From RO PlayGround Processor";
        }
        return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
    }

    public void RejectChanges()
    {
        var entries = ChangeTracker.Entries().ToList();
        foreach (var entry in entries)
            switch (entry.State)
            {
                case EntityState.Modified:
                case EntityState.Deleted:
                    entry.State = EntityState.Modified; //Revert changes made to deleted entity.
                    entry.State = EntityState.Unchanged;
                    break;

                case EntityState.Added:
                    entry.State = EntityState.Detached;
                    break;
            }
    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
    }
}
