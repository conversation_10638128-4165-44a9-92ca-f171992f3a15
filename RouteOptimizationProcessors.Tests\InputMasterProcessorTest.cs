﻿using InputMasterProcessor;
using Microsoft.Extensions.DependencyInjection;
using RouteOptimizationProcessor.Core.Models.QueueModels;
using RouteOptimizationProcessor.Core.Services;
using Xunit;

namespace RouteOptimizationProcessors.Tests;
public class InputMasterProcessorTest : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly InputMasterContinuousProcessor _inputMasterProcessor;

    /// <summary>
    /// Constructor that runs before each test
    /// </summary>
    public InputMasterProcessorTest()
    {
        // Set up environment variables for testing
        Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageWritable.vault.azure.net/");

        // Get configuration
        var configuration = Configuration.GetConfiguration();

        // Set up dependency injection
        IServiceCollection serviceCollection = new ServiceCollection();
        InputMasterProcessor.Configurations.Dependencies.SetUp(
            configuration,
            serviceCollection
        );

        // Build service provider
        _serviceProvider = serviceCollection.BuildServiceProvider();

        // Get the Perfect Entity Service
        var inputMasterService = _serviceProvider.GetRequiredService<IInputMasterService>();

        // Create the triggered processor
        _inputMasterProcessor = new InputMasterContinuousProcessor(inputMasterService);
    }

    [Fact]
    public async Task Test()
    {
        try
        {
            var data = new RoutePlaygroundQueueModel
            {
                Id = 1,
            };

            await _inputMasterProcessor.ProcessQueueAsync(data);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            throw;
        }
    }


    /// <summary>
    /// Cleanup method that runs after each test
    /// </summary>
    public void Dispose()
    {
        // Dispose of the service provider
        _serviceProvider?.Dispose();
    }
}