﻿using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;

namespace Library.RedisEventPush
{
    /// <summary>
    /// Service for pushing events to Redis
    /// </summary>
    public class RedisEventPushService : IRedisEventPushService, IDisposable
    {
        // Keep the connection as static to maintain a single connection across the application
        private static readonly Lazy<ConnectionMultiplexer> _lazyConnection;

        // Static constructor to initialize the connection
        static RedisEventPushService()
        {
            _lazyConnection = new Lazy<ConnectionMultiplexer>(() =>
            {
                // The connection string will be set via Initialize before this is accessed
                Console.WriteLine("Connecting to Redis...");
                var connection = ConnectionMultiplexer.Connect(_connectionOptions);
                Console.WriteLine("Connected to Redis successfully");
                return connection;
            });
        }

        private static string _connectionString;
        private static ConfigurationOptions _connectionOptions;

        /// <summary>
        /// Constructor that takes the connection string (injected via DI)
        /// </summary>
        /// <param name="redisConnectionString">Redis connection string</param>
        public RedisEventPushService(string redisConnectionString)
        {
            if (string.IsNullOrEmpty(redisConnectionString))
                throw new ArgumentNullException(nameof(redisConnectionString));

            if (_lazyConnection.IsValueCreated)
                throw new InvalidOperationException("Cannot set connection string after connection has been created");

            // Parse the connection string to configure options
            var options = ConfigurationOptions.Parse(redisConnectionString);

            // Add retry policy for connection reliability
            options.ConnectRetry = 3;
            options.AbortOnConnectFail = false;

            // Allow retrying failed operations
            options.ReconnectRetryPolicy = new LinearRetry(100); // Retry every 100ms

            _connectionString ??= redisConnectionString; // Only set if not already set
            _connectionOptions ??= options;
        }

        private static ConnectionMultiplexer Connection => _lazyConnection.Value;


        public async Task<long> WriteRecordsToRedisAsync<T>(string tableName, List<T>? records)
        {
            if (records is null || records.Count == 0)
            {
                return 0;
            }

            // Get the database
            var db = Connection.GetDatabase();

            List<RedisValue> redisValues = [];

            redisValues.
                AddRange(records.Select(record => JsonSerializer.Serialize(record))
                    .Select(serializedRecord => new RedisValue(serializedRecord)));

            return await db.ListRightPushAsync(tableName, redisValues.ToArray()).ConfigureAwait(false);
        }


        /// <summary>
        /// This should rarely be used, as we want to keep the connection open for the life of the process
        /// </summary>
        public void Dispose()
        {
            // Individual instances don't own the connection
        }

        /// <summary>
        /// Static method for application shutdown
        /// </summary>
        public static void CloseConnection()
        {
            if (_lazyConnection.IsValueCreated)
            {
                _lazyConnection.Value.Dispose();
            }
        }
    }
}
