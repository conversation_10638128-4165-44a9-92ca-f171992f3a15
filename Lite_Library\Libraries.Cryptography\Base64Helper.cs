﻿using System;
using System.Text;

namespace Libraries.Cryptography;

public class Base64AuthModel
{
    public string Password { get; set; }
    public string UserName { get; set; }
}

public class Base64Helper
{
    public static string Decrypt(string password)
    {
        var data = Convert.FromBase64String(password);
        var base64Decoded = Encoding.ASCII.GetString(data);
        return base64Decoded;
    }

    public static Base64AuthModel GetAuthorisedModel(string token)
    {
        var encoding = Encoding.GetEncoding("iso-8859-1");
        _ = encoding.GetString(Convert.FromBase64String(token));
        var credentials = encoding.GetString(Convert.FromBase64String(token));

        var separator = credentials.IndexOf(':');
        return new Base64AuthModel { UserName = credentials.Substring(0, separator), Password = credentials.Substring(separator + 1) };
    }

    public static string GetBasicToken(Base64AuthModel data)
    {
        var plainTextBytes = Encoding.GetEncoding("iso-8859-1").GetBytes($"{data.UserName}:{data.Password}");
        return Convert.ToBase64String(plainTextBytes);
    }
}
