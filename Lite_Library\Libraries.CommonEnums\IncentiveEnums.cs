﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum IncentiveTargetAchValueType
{
    StdUnit,
    Unit,
    Revenue,
    OutletCount
}

public enum QualifierType
{
    [Display(Name = "Target vs Achievement")]
    TargetvsAchievement = 1,

    [Display(Name = "Monthly Unique Billers")]
    MonthlyUniqueBillers = 2,

    [Display(Name = "Range Selling")]
    RangeSelling = 3,

    [Display(Name = "Penentration")]
    Penentration = 4,

    [Display(Name = "Reach")]
    Reach = 5,

    [Display(Name = "Growth")]
    Growth = 6,

    [Display(Name = "MT Innerwear and Accessories")]
    MTInnerwearAndAccessories = 7,

    [Display(Name = "MT Bra")]
    MTBra = 8,

    [Display(Name = "MT Outerwear")]
    MTOuterwear = 9,

    [Display(Name = "Daily Unique Biller")]
    DailyUniqueBiller = 10,

    [Display(Name = "MT Attendance Incentive")]
    MTAttendance = 50,

    [Display(Name = "MT Inward Incentive")]
    MTInwardIncentive = 51,

    [Display(Name = "MT Tertiary Incentive")]
    MTTertiaryIncentive = 52
}

public enum IncentiveDataType
{
    [Display(Name = "Percent (%)")]
    Percent = 1,

    [Display(Name = "Number")]
    Number = 2
}

public enum IncentivePayoutType
{
    [Display(Name = "Flat Value")]
    Flat = 1,

    [Display(Name = "Per Unit")]
    PerUnit = 2
}

public enum PayOutFrequency
{
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Annually = 4
}
