﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum RecommendationType
{
    Upsell = 0,
    Conventional = 1,
    Newproduct = 2,
    Focussed = 3
}

public enum BoughtTag
{
    Bought = 0,
    Not_Bought = 1
}

public enum StockStatus
{
    Never_Bought = 0,
    Check_Status = 1,
    Stock_Present = 2
}

public enum RecommendedProductCount
{
    [Display(Name = "Absolute Count")]
    AbsoluteCount = 0,

    [Display(Name = "Avg. LPC (Mark Up Percent)")]
    AvgLPC_MarkUpPercent = 1,

    [Display(Name = "Avg. LPC (Mark Down Percent)")]
    AvgLPC_MarkDownPercent = 2
}

public enum RecommendationTag
{
    Upsell,
    Focused,
    Mandatory,
    NPD,
    Opportunity
}
