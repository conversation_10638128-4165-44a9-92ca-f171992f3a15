﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum DerivedKPIPerspective
{
    [Display(Name = "User Performance")]
    UserPerformance = 1,

    [Display(Name = "Product Performance")]
    ProductPerformance = 2,

    [Display(Name = "Outlet Performance")]
    OutletPerformance = 3,

    [Display(Name = "L3M User performance")]
    L3MUserperformance = 4,

    [Display(Name = "Day Summary")]
    DaySummary = 5,

    [Display(Name = "MOM User performance")]
    MOMUserperformancee = 10,

    [Display(Name = "Outlet wise Demand vs Sales (Alpha)")]
    SecondaryDemandVsSales = 18,

    [Display(Name = "Product Demand vs Sales (Alpha)")]
    ProductDemandVsSales = 19,

    [Display(Name = "Distributor Performance (Alpha)")]
    DistributorPerformance = 20,

    [Display(Name = "Distributor Product Performance")]
    DistributorProductPerformance = 21,

    [Display(Name = "Out of Stock")]
    OutOfStock = 50,

    [Display(Name = "Shelf Share")]
    ShelfShare = 51
}
