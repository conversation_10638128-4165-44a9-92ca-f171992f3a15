﻿using EntityHelper;
using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace RouteOptimizationProcessor.Core.Models.DbModels;

public class RoutePlan : ICreatedEntity
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public DateTime EffectiveDate { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public long EmployeeId { get; set; }
    public bool IsDeactive { get; set; }
    public bool Deleted { get; set; }
    public ICollection<RoutePlanItem> RoutePlanItems { get; set; }
    public JourneyFrequency RepeatFrequency { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
}
public class RoutePlanItem
{
    public long Id { get; set; }
    public int DayNumber { get; set; }
    public long RoutePlanId { get; set; }
    public long? RouteId { get; set; }
    public string? ReasonCategory { get; set; }
    public string? Reason { get; set; }
    public Route Route { get; set; }
    public long? JWFieldUserId { get; set; }
    public RoutePlan RoutePlan { get; set; }

}

public class Route : IAuditedEntity
{
    public long Id { get; set; }
    public string? Name { get; set; }
    public string? ErpId { get; set; }
    public bool IsDeactive { get; set; }
    public bool Deleted { get; set; }
    public Guid RouteGuid { get; set; }
    public virtual ICollection<RouteOutletMapping> RouteOutletMappings { get; set; }
    public long CompanyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}
public class RouteOutletMapping : IAuditedEntity
{
    public long Id { get; set; }
    [ForeignKey("Routes")]
    public long RouteId { get; set; }
    public long LocationId { get; set; }
    public bool Deleted { get; set; }
    public long CompanyId { get; set; }
    public string? CreationContext { get; set; }
    public int? VisitOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }
}