﻿using Libraries.CommonEnums;

namespace RouteOptimizationProcessor.Core.Models.CoreModels;
public class PositionCodeEntityMappingMin
{
    public long EntityId { get; set; }
    public bool IsDetached { get; set; }
    public PortalUserRole EntityRole { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }
    public string PositionCode { get; set; }
    public string Position { get; set; }
    public string UserName { get; set; }
    public long PositionId { get; set; }
    public long? ParentPositionId { get; set; }
}

public class PositionBeatModel
{
    public long PositionId { get; set; }
    public long BeatId { get; set; }
}

public class EmployeeBeatModel
{
    public long EmployeeId { get; set; }
    public long BeatId { get; set; }
}

public class PositionRouteModel
{
    public long PositionId { get; set; }
    public long RouteId { get; set; }
}
