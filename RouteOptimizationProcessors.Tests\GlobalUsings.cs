global using Xunit;
global using Moq;
global using FluentAssertions;
global using Microsoft.EntityFrameworkCore;
global using Libraries.CommonEnums;
global using Libraries.CommonModels;
global using RouteOptimizationProcessor.Core.Models.CoreModels;
global using RouteOptimizationProcessor.Core.Models.DbModels;
global using RouteOptimizationProcessor.Core.Models.QueueModels;
global using RouteOptimizationProcessor.Core.Repositories;
global using RouteOptimizationProcessor.Core.Services;
global using RouteOptimizationProcessor.DbStorage.DbContexts;
global using RouteOptimizationProcessor.DbStorage.Repositories;
