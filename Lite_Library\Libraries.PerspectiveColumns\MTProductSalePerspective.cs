﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class MTProductSalePerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public MTProductSalePerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager,
            Name = "GSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.Employee,
            Name = "ESM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Reporting Manager",
            Name = "ReportingManager",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Name",
            Name = "FieldUserName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User ERP ID",
            Name = "FieldUserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position}",
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position}",
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position}",
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position}",
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position}",
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position}",
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position}",
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position}",
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Reporting Manager",
            Name = "L2Position_ReportingUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser Name",
            Name = "L1Position_FieldUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Field User ERP ID",
            Name = "L1Position_UserERP",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat,
            Name = "Beat",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Territory,
            Name = "Territory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level5,
            Name = "Level5",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level6,
            Name = "Level6",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level7,
            Name = "Level7",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets,
            Name = "Shop",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " ERPID",
            Name = "ShopERPID",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Owners Name",
            Name = "ShopOwnersName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Owners Number",
            Name = "ShopOwnersNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Address",
            Name = "ShopAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Market",
            Name = "ShopMarket",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Town",
            Name = "ShopSubCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " City",
            Name = "ShopCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " State",
            Name = "ShopState",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Type",
            Name = "ShopType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Segmentation",
            Name = "ShopSegmentation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        //new PerspectiveColumnModel() { Attribute = linkNames.Outlets , DisplayName = "Focussed "+linkNames.Outlets, Name = "FocussedShop", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
        new()
        {
            Attribute = "Visit",
            DisplayName = "CheckIn Id",
            Name = "AttendanceId",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "Conversion Factor",
            Name = "ProductStandardUnitConversionFactor",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "Std. Unit",
            Name = "ProductStandardUnit",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "Unit",
            Name = "ProductUnit",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Chain",
            DisplayName = "Chain Name",
            Name = "ChainName",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "ProductMRP",
            Name = "ProductMRP",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "ProductERPID",
            Name = "ProductERPID",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "ProductVariant",
            Name = "ProductVariant",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "Product",
            Name = "ProductName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new()
        {
            Attribute = "Product",
            DisplayName = linkNames.SecondaryCategory,
            Name = "SecondaryCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new()
        {
            Attribute = "Product",
            DisplayName = linkNames.PrimaryCategory,
            Name = "PrimaryCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "Style",
            Name = "DisplayCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            pivotCategory = PivotCategory.Category
        },
        //new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Product Divison" , Name = "ProductDivision",  IsMeasure = true,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true },
        //new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "PTR" , Name = "PTR", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        //new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Original PTR" , Name = "OriginalPTR", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new()
        {
            Attribute = "Time",
            DisplayName = "Date",
            Name = "Date",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Time",
            Name = "Time",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            NotForReports = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Close to Expiry Stock Qty",
            Name = "NearExpiryStockQuantity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Tertiary Sales ({linkNames.Unit})",
            Name = "TertiarySalesInUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Tertiary Sales ({linkNames.StdUnit})",
            Name = "TertiarySalesInStdUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Tertiary Sales (Revenue)",
            Name = "TertiarySalesValue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Calclulated Closing Stock ({linkNames.Unit})",
            Name = "CalculatedClosingStockInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Calclulated Closing Stock ({linkNames.StdUnit})",
            Name = "CalculatedClosingStockInStdUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Calclulated Closing Stock (Revenue)",
            Name = "CalculatedClosingStockInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Closing Stock ({linkNames.Unit})",
            Name = "ClosingStockInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Closing Stock ({linkNames.StdUnit})",
            Name = "ClosingStockInStdUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Closing Stock (Revenue)",
            Name = "ClosingStockInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Opening Stock Value",
            Name = "OpeningStockInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Opening Stock Qty ({linkNames.StdUnit})",
            Name = "OpeningStockInStdUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Opening Stock Qty ({linkNames.Unit})",
            Name = "OpeningStockQty",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Inward Stock Value",
            Name = "InwardStockInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Inward Stock Qty ({linkNames.StdUnit})",
            Name = "InwardStockInStdUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Inward Stock Qty ({linkNames.Unit})",
            Name = "InwardStockQty",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Return Quantity",
            Name = "ReturnQuantity",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Day Start",
            DisplayName = "Present Users",
            Name = "PresentUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Day Start",
            DisplayName = "Retailing",
            Name = "RetailingUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Day Start",
            DisplayName = "Official Work",
            Name = "OfficialWorkUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Day Start",
            DisplayName = "Leave",
            Name = "LeaveUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Day Start",
            DisplayName = "Weekly Off",
            Name = "WEEklyOffUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Day Start",
            DisplayName = "Absent Users",
            Name = "AbsentUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Per man-day per store sales ({linkNames.Unit})",
            Name = "TertiarySalesInUnit_CheckIns",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Per man-day per store sales ({linkNames.StdUnit})",
            Name = "TertiarySalesInStdUnit_CheckIns",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Per man-day per store sales (Value)",
            Name = "TertiarySalesValue_CheckIns",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Avg man-day thruput ({linkNames.Unit})",
            Name = "TertiarySalesInUnit_RetailingUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Avg man-day thruput ({linkNames.StdUnit})",
            Name = "TertiarySalesInStdUnit_RetailingUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg man-day thruput (Value)",
            Name = "TertiarySalesValue_RetailingUsers",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Targets",
            Name = "Targets",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        }
    };
}
