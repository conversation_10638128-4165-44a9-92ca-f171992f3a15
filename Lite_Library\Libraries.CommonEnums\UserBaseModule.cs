﻿namespace Libraries.CommonEnums;

public enum UserBaseModule
{
    // ISR Modules
    OpeningStocks = 1,

    //Returns = 2,
    StockInward = 3,
    TertiarySales = 4,
    TasksISR = 5,
    TimelineISR = 6,
    PerformanceISR = 7,
    PocketMISISR = 8,
    ExternalAssetsISR = 9,
    ClosingStock = 20,

    // Supervisor/Merchandiser Modules
    StockCapture = 10,
    PurchaseOrder = 11,
    TertiarySalesSupervisor = 12,
    TasksSupervisor = 13,
    TimelineSupervisor = 14,
    PerformanceSupervisor = 15,
    PocketMISSupervisor = 16,
    FillRate = 17,

    ExternalAssetsSupervisor = 18
    //AlertsNotification = 19
}
