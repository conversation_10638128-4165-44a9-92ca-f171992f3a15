<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="xunit" Version="2.6.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\InputMasterProcessor\InputMasterProcessor.csproj" />
    <ProjectReference Include="..\RouteOptimizationProcessor.Core\RouteOptimizationProcessor.Core.csproj" />
    <ProjectReference Include="..\RouteOptimizationProcessor.DbStorage\RouteOptimizationProcessor.DbStorage.csproj" />
    <ProjectReference Include="..\Lite_Library\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Lite_Library\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.StorageWriter\Library.StorageWriter.csproj" />
  </ItemGroup>

</Project>
