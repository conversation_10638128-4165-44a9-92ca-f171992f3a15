﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace RouteOptimizationProcessor.Core.Models.DbModels;


[Table("PositionCodes")]
public class PositionCode
{
    public PositionCode()
    {
        PositionCodeEntityMappings = new HashSet<PositionCodeEntityMapping>();
    }
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string CodeId { get; set; }
    public string Name { get; set; }
    public PositionCodeLevel Level { get; set; }
    public long? ParentId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool Deleted { get; set; }
    public PositionCode Parent { get; set; }
    public ICollection<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
    public ICollection<PositionBeatMapping> PositionBeatMappings { get; set; }
    public ICollection<PositionDistributorMapping> PositionDistributorMappings { get; set; }
}

[Table("PositionBeatMapping")]
public class PositionBeatMapping
{
    public long Id { get; set; }
    [ForeignKey("PositionCode")]
    public long PositionId { get; set; }
    [ForeignKey("LocationBeat")]
    public long BeatId { get; set; }
    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }
    public long CompanyId { get; set; }
    public string? CreationContext { get; set; }
    public bool IsDeleted { get; set; }
    public virtual PositionCode PositionCode { get; set; }
    public virtual LocationBeat LocationBeat { get; set; }
    public long? ProductDivisionId { get; set; }
    public bool IsSecondaryMapping { get; set; }
    public bool IsTemporaryAttachment { get; set; }
}

public class PositionCodeEntityMapping
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long PositionCodeId { get; set; }
    public long EntityId { get; set; }

    [Column("IsDetached")]
    public bool IsDeactive { get; set; }

    public DateTime CreatedAt { get; set; }
    public string? CreationContext { get; set; }
    public PositionCode PositionCode { get; set; }
}
public class LocationBeat
{
    public long Id { get; set; }
    //public bool Deleted { get; set; }
    public bool IsDeactive { get; set; }
    public string? Name { get; set; }
    public long? TerritoryId { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long Company { get; set; }
    public string? ErpId { get; set; }
}

public class PositionDistributorMapping
{
    public long Id { get; set; }
    [ForeignKey("PositionCode")]
    public long PositionId { get; set; }
    public long DistributorId { get; set; }

    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [ForeignKey("Company")]
    public long CompanyId { get; set; }
    public string CreationContext { get; set; }
    public bool IsDeleted { get; set; }

    public virtual PositionCode PositionCode { get; set; }
}