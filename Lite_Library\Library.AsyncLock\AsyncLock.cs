﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Library.AsyncLock;

public sealed class AsyncLock<T>
{
    private const int waitTimePerAttemptMillis = 50;
    private readonly HashSet<T> employeesBeingProcessed = new();
    private readonly object locker = new();
    private readonly int maxAllowedAttempts;

    public AsyncLock(int maxWaitSeconds = 10)
    {
        maxAllowedAttempts = maxWaitSeconds * 1000 / waitTimePerAttemptMillis;
    }

    private async Task<bool> AttemptLock(T empId, AttemptValidator av)
    {
        while (employeesBeingProcessed.Contains(empId) && av.NextAttempt())
        {
            await Task.Delay(waitTimePerAttemptMillis).ConfigureAwait(false);
        }

        lock (locker)
        {
            if (!employeesBeingProcessed.Contains(empId))
            {
                employeesBeingProcessed.Add(empId);
                return true;
            }

            return false;
        }
    }

    public async Task<IDisposable> LockAsync(T empId)
    {
        //todo:Add exception after a number of tries
        var av = new AttemptValidator(maxAllowedAttempts);
        while (!await AttemptLock(empId, av).ConfigureAwait(false))
        {
            if (av.HasExceeded())
            {
                throw new MaxLockAttemptExceededException();
            }
        }

        return new Releaser(this, empId);
    }

    private sealed class AttemptValidator
    {
        private readonly int maxAttempts;
        private int currentAttempts;

        public AttemptValidator(int maxAttempts)
        {
            this.maxAttempts = maxAttempts;
        }

        public bool HasExceeded()
        {
            return currentAttempts > maxAttempts;
        }

        public bool NextAttempt()
        {
            currentAttempts++;
            return !HasExceeded();
        }
    }

    private sealed class Releaser : IDisposable
    {
        private readonly AsyncLock<T> asyncLock;
        private readonly T employeeToRealease;

        internal Releaser(AsyncLock<T> asyncLock, T employeeToRealease)
        {
            this.asyncLock = asyncLock;
            this.employeeToRealease = employeeToRealease;
        }

        public void Dispose()
        {
            lock (asyncLock.locker)
            {
                if (asyncLock.employeesBeingProcessed.Contains(employeeToRealease))
                {
                    asyncLock.employeesBeingProcessed.Remove(employeeToRealease);
                }
            }
        }
    }
}
