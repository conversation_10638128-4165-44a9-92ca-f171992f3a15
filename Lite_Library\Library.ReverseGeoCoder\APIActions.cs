﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using Library.SlackService;

namespace Library.ReverseGeoCoder;

public class APIActions<T> where T : class
{
    private readonly HttpClient _client;
    private readonly ErrorMessenger _errorMessanger;
    private readonly string channel;

    public APIActions(ErrorMessenger errorMessanger, string authToken = null, bool isRawAuth = false, int? timeout = null, string channel = "")
    {
        _client = new HttpClient();
        if (timeout.HasValue)
        {
            _client.Timeout = TimeSpan.FromSeconds(timeout.Value);
        }

        if (authToken != null)
        {
            _client.DefaultRequestHeaders.Authorization = isRawAuth ? new AuthenticationHeaderValue(authToken.Split(' ')[0], authToken.Split(' ')[1]) : new AuthenticationHeaderValue("Bearer", authToken);
        }

        _errorMessanger = errorMessanger;
        this.channel = channel;
    }

    public async Task<T> Get(string api, int retryCount = 0)
    {
        HttpResponseMessage response;
        do
        {
            response = await _client.GetAsync(api).ConfigureAwait(false);
            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                var data = JsonSerializer.Deserialize<T>(responseBody);
                return data;
            }

            retryCount--;
            await Task.Delay(3000).ConfigureAwait(false);
        } while (retryCount >= 0);

        var errorBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        await _errorMessanger.SendToSlack($"Dependency Failure at {api}", errorBody, "appapi_geocoding_issues").ConfigureAwait(false);
        throw new Exception($"Dependency Failed to respond Properly at {api}");
    }
}
