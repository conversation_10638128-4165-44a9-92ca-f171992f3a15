﻿using System;

namespace Libraries.CommonEnums;

public enum HierarchyLevel
{
    ESM = 0,
    ASM = 1,
    RSM = 2,
    ZSM = 3,
    NSM = 4,
    GSM = 5
}

public enum PositionCodeLevel
{
    L8Position = 8,
    L7Position = 9,
    L6Position = 10,
    L5Position = 20,
    L4Position = 30,
    L3Position = 40,
    L2Position = 50,
    L1Position = 60
}

[Obsolete("Try to use PositionCodeLevel instead")]
public enum PositionLevel
{
    Level8 = 8,
    Level7 = 9,
    Level6 = 10,
    Level5 = 20,
    Level4 = 30,
    Level3 = 40,
    Level2 = 50,
    Level1 = 60
}
