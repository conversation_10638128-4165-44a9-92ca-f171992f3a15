﻿using Libraries.CommonEnums;
using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;


namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class EmployeeRepository(MasterDbContext masterDbContext) : IEmployeeRepository
{
    private IQueryable<ClientEmployee> GetFieldUsersQueryable(long companyId, bool isIncludeDeactiveUsers = false)
    {
        return GetEmployeeQueryable(companyId, isIncludeDeactiveUsers)
            .Where(e => e.<PERSON><PERSON>)
            .OrderByDescending(e => e.Id);
    }

    private IQueryable<ClientEmployee> GetEmployeeQueryable(long companyId, bool isIncludeDeactiveUsers = false)
    {
        return isIncludeDeactiveUsers
            ? masterDbContext.ClientEmployees.Where(e => e.CompanyId == companyId && !e.IsTrainingUser)
            : masterDbContext.ClientEmployees.Where(e => e.CompanyId == companyId && !e.IsDeactive && !e.IsTrainingUser);
    }

    private IQueryable<ClientEmployee> GetFieldUserUnderRoleQueryable(long companyId, List<long> userIds,
        PortalUserRole portalUserRole, bool includeInactive = false)
    {
        return GetFieldUsersQueryable(companyId, includeInactive).Where(u =>
            (userIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id) &&
             u.Parent.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Parent.Parent.Parent.Id) &&
                u.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Parent.Parent.Id) && u.Parent.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Parent.Id) && u.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Id) && u.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Id) && u.UserRole == portalUserRole)
            || (userIds.Contains(u.Id) && PortalUserRole.ClientEmployee == portalUserRole)
            || portalUserRole == PortalUserRole.CompanyAdmin || portalUserRole == PortalUserRole.CompanyExecutive ||
            portalUserRole == PortalUserRole.AccountManager || portalUserRole == PortalUserRole.GlobalAdmin);
    }

    public async Task<List<EmployeeMinWithRank>> GetFieldUserIdsUnderManagerModelAsync(long companyId, PortalUserRole userRole, List<long> userIds,
        EmployeeType? userType = null)
    {
        return await GetFieldUserUnderRoleQueryable(companyId, userIds, userRole)
            .Where(u => (userType.HasValue && u.UserType == userType)
                        || !userType.HasValue).Select(e =>
                new EmployeeMinWithRank
                {
                    Id = e.Id,
                    UserRank = e.Rank
                }).ToListAsync();
    }

    public async Task<List<EmployeeMinWithType>> GetAllEmployeesAsync(long companyId)
    {
        return await masterDbContext.ClientEmployees
            .Where(e => e.CompanyId == companyId
                        && !e.IsDeactive)
            .Select(e => new EmployeeMinWithType
            {
                Id = e.Id,
                Name = e.Name,
                UserType = e.UserType,
            }).ToListAsync();
    }

    public async Task<List<EmployeeMinWithType>> GetAllEmployeesSRAndDSRAsync(long companyId)
    {
        return await masterDbContext.ClientEmployees
            .Where(e => e.CompanyId == companyId
                        && !e.IsDeactive && e.IsFieldAppuser
                        && (e.UserType == EmployeeType.SR || e.UserType == EmployeeType.DSR))
            .Select(e => new EmployeeMinWithType
            {
                Id = e.Id,
                Name = e.Name,
                UserType = e.UserType
            }).ToListAsync();
    }

    public async Task<Dictionary<long, string>> GetEmployeeErpIdDictionaryAsync(long companyId, List<long> employeeIds)
    {
        return await masterDbContext.ClientEmployees
            .Where(e => e.CompanyId == companyId && employeeIds.Contains(e.Id) && !e.IsDeactive)
            .Select(e => new { e.Id, e.ErpId })
            .ToDictionaryAsync(e => e.Id, e => e.ErpId);
    }

    public async Task<(List<long>, List<long>, List<PositionCodeLevel>)> GetPositionCodesOfAndUnderUserAsync(long companyId, long employeeId)
    {
        var userPositions = masterDbContext.PositionCodeEntityMappings.Where(a => a.CompanyId == companyId && !a.IsDeactive && !a.PositionCode.Deleted && a.EntityId == employeeId).Select(a => new { a.PositionCodeId, a.PositionCode.Level }).OrderBy(p => p.Level);
        var positionsOfAndUnderUser = await masterDbContext.PositionCodes.Where(m => m.CompanyId == companyId && !m.Deleted && userPositions.Any(a => a.PositionCodeId == m.Id ||
                                                                                                                                         a.PositionCodeId == m.ParentId ||
                                                                                                                                         a.PositionCodeId == m.Parent.ParentId ||
                                                                                                                                         a.PositionCodeId == m.Parent.Parent.ParentId ||
                                                                                                                                         a.PositionCodeId == m.Parent.Parent.Parent.ParentId ||
                                                                                                                                         a.PositionCodeId == m.Parent.Parent.Parent.Parent.ParentId ||
                                                                                                                                         a.PositionCodeId == m.Parent.Parent.Parent.Parent.Parent.ParentId ||
                                                                                                                                         a.PositionCodeId == m.Parent.Parent.Parent.Parent.Parent.Parent.ParentId
        )).Select(m => m.Id).Distinct().ToListAsync();
        var userPositionList = userPositions.ToList();
        return (userPositionList.Select(p => p.PositionCodeId).ToList(), positionsOfAndUnderUser, userPositionList.Select(p => (PositionCodeLevel)p.Level).ToList());
    }

    public async Task<List<EntityMinWithErp>> GetBeatsForPositionsAsync(long companyId, List<long> positionIds)
    {
        return await masterDbContext.PositionBeatMapping.Where(m => m.CompanyId == companyId && !m.IsDeleted &&
                                                       positionIds.Contains(m.PositionId) && !m.LocationBeat.IsDeactive).Select(m => new EntityMinWithErp
                                                       {
                                                           Id = m.LocationBeat.Id,
                                                           Name = m.LocationBeat.Name,
                                                           ErpId = m.LocationBeat.ErpId,
                                                       }).ToListAsync();
    }

    public async Task<List<EntityMinWithErp>> GetBeatsForEmployeesAsync(long employeeId, long companyId)
    {
        var data = await masterDbContext.FAEmployeeBeatMappings.Where(s => s.CompanyId == companyId && s.EmployeeId == employeeId && !s.IsDeleted)
            .Join(masterDbContext.LocationBeats.Where(s => s.Company == companyId && !s.IsDeactive), m => m.BeatId, b => b.Id, (m, b) => new { m, b })
            .Select(s => new EntityMinWithErp() { Id = s.b.Id, Name = s.b.Name, ErpId = s.b.ErpId }).ToListAsync();
        return data;
    }

    public async Task<List<EntityMinWithErp>> GetRoutesForEmployeeAsync(long companyId, long employeeId)
    {
        var today = DateTime.UtcNow.Date;
        return await masterDbContext.RoutePlans.Where(rp => rp.CompanyId == companyId && rp.EmployeeId == employeeId
                            && rp.EffectiveDate <= today.Date && (rp.EndDate == null || rp.EndDate >= today.Date)
                            && !rp.IsDeactive && !rp.Deleted).SelectMany(s => s.RoutePlanItems.Where(r => r.RouteId.HasValue).Select(r => new EntityMinWithErp
                                                                         {
                                                                             ErpId = r.Route.ErpId,
                                                                             Id = r.RouteId.Value,
                                                                             Name = r.Route.Name
                                                                         })).ToListAsync();
    }

    public async Task<List<EntityMinWithErp>> GetRoutesForEmployeeViaDirectMappingAsync(long companyId, long employeeId)
    {
        return await masterDbContext.FAEmployeeRouteMappings.Where(e => e.EmployeeId == employeeId && e.CompanyId == companyId && !e.IsDeleted)
            .Select(e => new EntityMinWithErp
            {
                Id = e.Route.Id,
                Name = e.Route.Name,
                ErpId = e.Route.ErpId
            }).ToListAsync();
    }

    public async Task<List<EntityMinWithErp>> GetRoutesForPositions(long companyId, List<long> positionIds)
    {
        return await masterDbContext.RoutePositionMappings
            .Where(m => m.CompanyId == companyId && !m.Deleted && positionIds.Contains(m.PositionCodeId) &&
                        !m.Routes.IsDeactive)
            .Select(m => new EntityMinWithErp { Id = m.Routes.Id, Name = m.Routes.Name, ErpId = m.Routes.ErpId, })
            .Distinct().ToListAsync();
    }
}