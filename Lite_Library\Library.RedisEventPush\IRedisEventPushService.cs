﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Library.RedisEventPush
{
    /// <summary>
    /// Interface for Redis event push service that allows saving data to Redis lists
    /// </summary>
    public interface IRedisEventPushService : IDisposable
    {
        
        Task<long> WriteRecordsToRedisAsync<T>(string tableName, List<T>? records);

        /// <summary>
        /// Closes the Redis connection
        /// </summary>
        static void CloseConnection() => throw new NotImplementedException();
    }
}
