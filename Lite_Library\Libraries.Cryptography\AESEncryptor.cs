﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Libraries.Cryptography;

public class AESEncryptor
{
    private const string _hash = "SHA1";
    private const int _iterations = 2;
    private const int _keySize = 256;
    private const string _salt = "aselrias38490a32"; // Random
    private const string _vector = "8947az34awl34kjq"; // Random

    public static string Encrypt(string value, string password)
    {
        var vectorBytes = Encoding.UTF8.GetBytes(_vector);
        var saltBytes = Encoding.UTF8.GetBytes(_salt);
        var valueBytes = Encoding.UTF8.GetBytes(value);

        byte[] encrypted;
        using (var cipher = Aes.Create())
        {
            var _passwordBytes =
                new PasswordDeriveBytes(password, saltBytes, _hash, _iterations);
            var keyBytes = _passwordBytes.GetBytes(_keySize / 8);

            cipher.Mode = CipherMode.CBC;

            using (var encryptor = cipher.CreateEncryptor(keyBytes, vectorBytes))
            {
                using (var to = new MemoryStream())
                {
                    using (var writer = new CryptoStream(to, encryptor, CryptoStreamMode.Write))
                    {
                        writer.Write(valueBytes, 0, valueBytes.Length);
                        writer.FlushFinalBlock();
                        encrypted = to.ToArray();
                    }
                }
            }

            cipher.Clear();
        }

        return Convert.ToBase64String(encrypted);
    }
}
