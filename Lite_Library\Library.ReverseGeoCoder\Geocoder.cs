﻿using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using Library.ResilientHttpClient;
using Library.StringHelpers;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Library.ReverseGeoCoder;

using JsonSerializer = JsonSerializer;

public static class Geocoder
{
    private const string googleApiKey = "AIzaSyDoPC-MNbaC47eeBE4CU3OORqGd9eExJBQ";

    public static async Task<string> GetAddressFromLatLng(decimal lat, decimal lng)
    {
        return (await GetDataFromLatLng(lat, lng).ConfigureAwait(false))?.Address ?? "Address not found";
    }

    public static async Task<double> GetDistanceBetweenTwoLocationsUsingExternalApi(decimal originLatitude = 0, decimal originLongitude = 0, decimal destinationLatitude = 0, decimal destinationLongitude = 0, string apiKey = "")
    {
        var client = new FAResilientHttpClient_Old();

        client.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json"));

        var api = $"https://maps.googleapis.com/maps/api/distancematrix/json?destinations={destinationLatitude},{destinationLongitude}&origins={originLatitude},{originLongitude}&key={apiKey}";

        var response = await client.GetAsync(api).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var data = JsonConvert.DeserializeObject<DistanceMatrixData>(await response.Content.ReadAsStringAsync().ConfigureAwait(false));
            return data.Rows.FirstOrDefault()?.Elements?.FirstOrDefault()?.Distance?.Value ?? 0;
        }

        return 0;
    }

    public static async Task<GeoCodedReturnData> GetDataFromLatLng(decimal lat, decimal lng)
    {
        var client = new FAResilientHttpClient_Old();

        client.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json"));

        var api = $"https://maps.googleapis.com/maps/api/geocode/json?key={googleApiKey}&latlng={lat},{lng}";

        var response = await client.GetAsync(api).ConfigureAwait(false);

        if (response.IsSuccessStatusCode)
        {
            var data = JsonSerializer.Deserialize<GoogleApiListObject>(await response.Content.ReadAsStringAsync().ConfigureAwait(false));
            return new GeoCodedReturnData
            {
                Address = data.GetAddress(),
                City = data.GetAdmin2(),
                Locality = data.GetLocality(),
                Latitude = lat,
                Longitude = lng,
                State = data.GetState(),
                PinCode = data.GetPinCode()
            };
        }

        return null;
    }
}

public class GeoCodedReturnData
{
    public string Address { set; get; }
    public string City { set; get; }
    public string Country { get; set; }
    public string District { get; set; }
    public decimal Latitude { get; set; }
    public string Locality { set; get; }
    public decimal Longitude { get; set; }
    public string PinCode { get; set; }
    public string State { set; get; }
}

[DataContract]
public class GoogleApiAddressComponents
{
    [DataMember(Name = "long_name")]
    public string Name { set; get; }

    [DataMember(Name = "types")]
    public List<string> Types { set; get; }
}

[DataContract]
public class GoogleApiListObject
{
    [DataMember(Name = "results")]
    public List<GoogleApiReturnData> Results { get; set; }

    public string GetAddress()
    {
        var address = string.Empty;
        if (!string.IsNullOrWhiteSpace(GetSubLocality()))
        {
            address = address + GetSubLocality() + ", ";
        }

        if (!string.IsNullOrWhiteSpace(GetLocality()))
        {
            address = address + GetLocality() + ", ";
        }

        if (!string.IsNullOrWhiteSpace(GetAdmin2()))
        {
            address = address + GetAdmin2() + ", ";
        }

        if (!string.IsNullOrWhiteSpace(GetState()))
        {
            address = address + GetState() + ", ";
        }

        if (!string.IsNullOrWhiteSpace(GetCountry()))
        {
            address += GetCountry();
        }

        return address;
    }

    public string GetAdmin2()
    {
        return Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("administrative_area_level_2")).Select(i => i.Name)).FirstOrDefault();
    }

    public string GetCountry()
    {
        var data = Results.Select(r => r.Data).FirstOrDefault();
        return data != null && data.Any() ? data.Where(d => d.Types.Contains("country")).Select(d => d.Name).FirstOrDefault() : null;
    }

    public string GetFullAddress()
    {
        return Results.Select(r => r.Address).FirstOrDefault();
    }

    public string GetLocality()
    {
        return Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("locality")).Select(i => i.Name)).FirstOrDefault();
    }

    public string GetPinCode()
    {
        var pincode = Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("postal_code") && i.Name != "[no name]").Select(i => i.Name)).FirstOrDefault();
        return pincode != null && pincode.Length > 6 ? StringHelper.ExtractPinCode(pincode) : pincode;
    }

    public string GetState()
    {
        var data = Results.Select(r => r.Data).FirstOrDefault();
        return data != null && data.Any()
            ? data.Where(d => d.Types.Contains("administrative_area_level_1")).Select(d => d.Name).FirstOrDefault()
            : null;
    }

    public string GetSubLocality()
    {
        return Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("sublocality")).Select(i => i.Name)).FirstOrDefault();
    }
}

public class DistanceMatrixData
{
    public string Status { get; set; }

    [JsonProperty(PropertyName = "origin_addresses")]
    public string[] OriginAddresses { get; set; }

    [JsonProperty(PropertyName = "destination_addresses")]
    public string[] DestinationAddresses { get; set; }

    public Row[] Rows { get; set; }

    public class Data
    {
        public double Value { get; set; }
        public string Text { get; set; }
    }

    public class Element
    {
        public string Status { get; set; }
        public Data Duration { get; set; }
        public Data Distance { get; set; }
    }

    public class Row
    {
        public Element[] Elements { get; set; }
    }
}

[DataContract]
public class GoogleApiReturnData
{
    [DataMember(Name = "formatted_address")]
    public string Address { set; get; }

    [DataMember(Name = "address_components")]
    public List<GoogleApiAddressComponents> Data { set; get; }

    [DataMember(Name = "types")]
    public List<string> Types { set; get; }
}
