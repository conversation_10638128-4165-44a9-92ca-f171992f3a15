﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;
using System.Globalization;

namespace RouteOptimizationProcessor.DbStorage.Repositories;
public class RoutePlanRepository(WritableMasterDbContext writableMasterDbContext) : IRoutePlanRepository
{
    public async Task SaveRoutePlan(List<AutomaticDJPDbModel> records, long companyId, DateTime startDate, DateTime currentDate, List<string> possibleWeekOffStrings,
        CancellationToken ct = default)
    {
        // Group into batches of 10 employees
        var employeeRoutesBatch = records
           .GroupBy(s => s.EmployeeId)
           .Select((group, index) => new
           {
               EmployeeId = group.Key,
               Items = group.ToList(),
               BatchIndex = index / 10
           })
           .GroupBy(x => x.BatchIndex)
           .ToList();

        foreach (var batch in employeeRoutesBatch)
        {
            await using var transaction = await writableMasterDbContext.Database.BeginTransactionAsync(ct);
            try
            {
                foreach (var employeeRoutes in batch)
                {
                    var routes = employeeRoutes.Items;
                    var empErpId = routes[0].EmployeeErpId;
                    var routePlanItems = new List<RoutePlanItem>();

                    var dateWiseEmpRoutes = routes
                        .GroupBy(s => s.Date)
                        .ToDictionary(
                            gr => gr.Key,
                            gr => gr.GroupBy(r => r.OutletId).Select(q => q.First()).ToList()
                        );

                    foreach (var dateWiseRoutes in dateWiseEmpRoutes)
                    {
                        var routeName = $"{empErpId}";
                        var isWeeklyOff = dateWiseRoutes.Value.Exists(s => possibleWeekOffStrings.Contains(s.OutletErpId));

                        long? routeId = null;
                        if (!isWeeklyOff)
                        {
                            // Inline CreateRoute
                            var yearWeekDayNumber = CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
                                dateWiseRoutes.Key,
                                CultureInfo.CurrentCulture.DateTimeFormat.CalendarWeekRule,
                                CultureInfo.CurrentCulture.DateTimeFormat.FirstDayOfWeek);

                            var routeIdSuffix = $"{yearWeekDayNumber}_{dateWiseRoutes.Key.Day}";
                            var routeNameWithSuffix = $"{routeName}_{routeIdSuffix}";

                            var routeOutletMappings = dateWiseRoutes.Value
                                .Select(s => new RouteOutletMapping
                                {
                                    LocationId = s.OutletId,
                                    CompanyId = companyId,
                                    Deleted = false,
                                    VisitOrder = s.Sequence
                                }).ToList();

                            var route = new Route
                            {
                                Name = routeNameWithSuffix,
                                ErpId = routeNameWithSuffix,
                                CompanyId = companyId,
                                IsDeactive = false,
                                RouteOutletMappings = routeOutletMappings,
                                RouteGuid = Guid.NewGuid()
                            };

                            await writableMasterDbContext.Routes.AddAsync(route, ct);
                            await writableMasterDbContext.SaveChangesAsync(ct);
                            routeId = route.Id;
                        }

                        // Inline CreatePlan
                        var dayNumber = (int)(dateWiseRoutes.Key - startDate).TotalDays + 1;
                        routePlanItems.Add(new RoutePlanItem
                        {
                            DayNumber = dayNumber,
                            RouteId = routeId,
                            ReasonCategory = isWeeklyOff ? "Weekly Off" : "Retailing"
                        });
                    }

                    // Inline CreateRoutePlan
                    var routePlan = new RoutePlan
                    {
                        CompanyId = companyId,
                        EmployeeId = employeeRoutes.EmployeeId,
                        StartDate = startDate,
                        EndDate = null,
                        IsDeactive = false,
                        Deleted = false,
                        RepeatFrequency = JourneyFrequency.OneWeek,
                        RoutePlanItems = routePlanItems
                    };

                    if (currentDate >= startDate)
                    {
                        routePlan.EffectiveDate = currentDate.AddDays(1);
                    }
                    else
                    {
                        routePlan.EffectiveDate = startDate;
                    }

                    var existingRoutePlans = await writableMasterDbContext.RoutePlans
                        .Where(s => s.CompanyId == companyId && s.EmployeeId == employeeRoutes.EmployeeId
                                    && !s.IsDeactive && !s.Deleted
                                    && (s.EndDate == null || s.EndDate >= routePlan.EffectiveDate))
                        .ToListAsync(ct);

                    foreach (var existingRoutePlan in existingRoutePlans)
                    {
                        existingRoutePlan.EndDate = routePlan.EffectiveDate.AddDays(-1);
                    }

                    await writableMasterDbContext.RoutePlans.AddAsync(routePlan, ct);
                    await writableMasterDbContext.SaveChangesAsync(ct);
                }

                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
    }
}
