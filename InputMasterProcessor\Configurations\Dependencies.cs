﻿using Library.Infrastructure.QueueService;
using Library.StorageWriter.Reader_Writer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RouteOptimizationProcessor.Core.Helpers;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.Core.Services;
using RouteOptimizationProcessor.DbStorage.DbContexts;
using RouteOptimizationProcessor.DbStorage.Repositories;


namespace InputMasterProcessor.Configurations;

public static class Dependencies
{
    public static void SetUp(IConfiguration config, IServiceCollection services)
    {
        #region Connection-Strings
        var masterDbConnectionString = config.GetConnectionString("MasterDbConnectionString");
        var writableMasterDbConnectionString = config.GetConnectionString(
            "WritableMasterDbConnectionString"
        );
        var writableTransactionDbConnectionString = config.GetConnectionString(
            "WritableTransactionDbConnectionString"
        );
        var transactionDbConnectionString = config.GetConnectionString(
            "TransactionDbConnectionString"
        );
        var nsDataApiConnectionString = config.GetConnectionString("NSDataApiConnectionString");
        var masterStorageConnectionString = config.GetConnectionString(
            "MasterStorageConnectionString"
        );
        var storageConnectionString = config.GetConnectionString("StorageConnectionString");
        var faiDataLakeConnectionString = config.GetConnectionString("FaiDataLakeConnectionString");
        #endregion

        #region DbContexts
        services.AddDbContext<MasterDbContext>(options => options.UseSqlServer(masterDbConnectionString));
        #endregion

        #region repos
        services.AddScoped<ICohortRepository, CohortRepository>();
        services.AddScoped<ICompanySettingsRepository, CompanySettingsRepository>();
        services.AddScoped<IEmployeeRepository, EmployeeRepository>();
        services.AddScoped<ILocationRepository, LocationRepository>();
        services.AddScoped<IPositionCodeEntityMappingRepository, PositionCodeEntityMappingRepository>();
        services.AddScoped<IRoutePlanAutomationRepository, RoutePlanAutomationRepository>();
        #endregion

        #region services
        services.AddScoped<ICohortService, CohortService>();
        services.AddScoped<IInputMasterService, InputMasterService>();
        services.AddScoped<ILocationService, LocationService>();
        #endregion

        #region infrastructure
        services.AddScoped(_ => new FaiDataLakeBlobWriter(faiDataLakeConnectionString));
        services.AddScoped(_ => new RoFileSplitQueueHandler(QueueType.RoPlaygroundFileSplitQueue, storageConnectionString));
        #endregion
    }
}
