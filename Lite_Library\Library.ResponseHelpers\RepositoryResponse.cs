﻿namespace Library.ResponseHelpers;

public class RepositoryResponse
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; }
    public string ExceptionMessage { get; set; }
    public Exception Exception { get; set; }
    public long? Id { get; set; }
    public Guid? Guid { get; set; }
    public List<long> Ids { get; set; }

    public static RepositoryResponse GetSuccessResponse(long? id = null, string message = "Success", Guid? guid = null)
    {
        return new RepositoryResponse { IsSuccess = true, Id = id, Message = message, Guid = guid };
    }

    public static RepositoryResponse GetSuccessResponse(Guid id, string message = "Success")
    {
        return new RepositoryResponse { IsSuccess = true, Guid = id, Message = message };
    }

    public static RepositoryResponse GetRejectResponse(long id, string message = "Failure")
    {
        return new RepositoryResponse { IsSuccess = false, Id = id, Message = message };
    }

    public static RepositoryResponse GetRejectResponse(string error)
    {
        return new RepositoryResponse { IsSuccess = false, Message = error };
    }

    public static RepositoryResponse GetRejectResponse(Exception ex)
    {
        return new RepositoryResponse { IsSuccess = false, Message = ex.Message, Exception = ex };
    }

    public static RepositoryResponse GetRejectResponse(string error, Exception ex)
    {
        return new RepositoryResponse { IsSuccess = false, Message = error, Exception = ex };
    }
}
