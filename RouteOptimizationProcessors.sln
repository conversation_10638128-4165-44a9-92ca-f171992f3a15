
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35716.79
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InputMasterProcessor", "InputMasterProcessor\InputMasterProcessor.csproj", "{6F3CBC09-906E-43EF-89B4-97C27D2CB151}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RouteOptimizationProcessor.Core", "RouteOptimizationProcessor.Core\RouteOptimizationProcessor.Core.csproj", "{41787847-A659-4CB1-9ECB-F96733D65BB9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RouteOptimizationProcessor.DbStorage", "RouteOptimizationProcessor.DbStorage\RouteOptimizationProcessor.DbStorage.csproj", "{8A3813B2-DD38-479A-A7B4-B8BD7A32640D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "FA_Libraries", "FA_Libraries", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EntityHelper", "Lite_Library\EntityHelper\EntityHelper.csproj", "{B190FC17-87DD-659A-A9DC-64D03680D2A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.CommonEnums", "Lite_Library\Libraries.CommonEnums\Libraries.CommonEnums.csproj", "{A7822C7B-C96F-832C-9DE3-680F5F63C834}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ConnectionStringParsor", "Lite_Library\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj", "{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.CommonModels", "Lite_Library\Libraries.CommonModels\Libraries.CommonModels.csproj", "{F19E5601-0CA0-7D01-06C5-C461D063812C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.StorageWriter", "Lite_Library\Library.StorageWriter\Library.StorageWriter.csproj", "{0014D456-3B46-9617-DFE9-2106D8B4753F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.Cryptography", "Lite_Library\Libraries.Cryptography\Libraries.Cryptography.csproj", "{F2EACC2D-EC80-2A30-B607-B2E3F9371494}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.NumberSystem", "Lite_Library\Library.NumberSystem\Library.NumberSystem.csproj", "{58DF80D3-535C-8F37-40CE-F9658392615A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.Infrastructure", "Lite_Library\Library.Infrastructure\Library.Infrastructure.csproj", "{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6F3CBC09-906E-43EF-89B4-97C27D2CB151}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F3CBC09-906E-43EF-89B4-97C27D2CB151}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F3CBC09-906E-43EF-89B4-97C27D2CB151}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F3CBC09-906E-43EF-89B4-97C27D2CB151}.Release|Any CPU.Build.0 = Release|Any CPU
		{41787847-A659-4CB1-9ECB-F96733D65BB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41787847-A659-4CB1-9ECB-F96733D65BB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41787847-A659-4CB1-9ECB-F96733D65BB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41787847-A659-4CB1-9ECB-F96733D65BB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A3813B2-DD38-479A-A7B4-B8BD7A32640D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A3813B2-DD38-479A-A7B4-B8BD7A32640D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A3813B2-DD38-479A-A7B4-B8BD7A32640D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A3813B2-DD38-479A-A7B4-B8BD7A32640D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Release|Any CPU.Build.0 = Release|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Release|Any CPU.Build.0 = Release|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B190FC17-87DD-659A-A9DC-64D03680D2A0} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{A7822C7B-C96F-832C-9DE3-680F5F63C834} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F19E5601-0CA0-7D01-06C5-C461D063812C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0014D456-3B46-9617-DFE9-2106D8B4753F} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{58DF80D3-535C-8F37-40CE-F9658392615A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {553A2702-B88B-4743-A48C-899C67B3D6DF}
	EndGlobalSection
EndGlobal
