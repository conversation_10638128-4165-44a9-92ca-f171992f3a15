﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum SIOrderStatus
{
    [Display(Name = "edited")]
    EDITED = 0,

    [Display(Name = "pending_for_approval")]
    PENDING_FOR_APPROVAL = 1,

    [Display(Name = "in_progress")]
    IN_PROGRESS = 2,

    [Display(Name = "completed")]
    COMPLETED = 3,

    [Display(Name = "cancelled")]
    CANCELLED = 4,

    [Display(Name = "deleted")]
    DELETED = 5,

    [Display(Name = "pending")]
    PENDING = 6,

    [Display(Name = "not_delivered")]
    NOT_DELIVERED,

    [Display(Name = "partially_delivered")]
    PARTIALLY_DELIVERED,

    [Display(Name = "partially_collected")]
    PARTIALLY_COLLECTED,

    [Display(Name = "partially_invoiced")]
    PARTIALLY_INVOICED,

    [Display(Name = "invoiced")]
    INVOICED,

    [Display(Name = "delivered")]
    DELIVERED,

    [Display(Name = "approved")]
    APPROVED,

    [Display(Name = "rejected")]
    REJECTED,

    [Display(Name = "accepted")]
    ACCEPTED,

    [Display(Name = "received")]
    RECEIVED,

    [Display(Name = "audit_pending")]
    AUDIT_PENDING,

    [Display(Name = "partially_audit")]
    PARTIALLY_AUDIT,

    [Display(Name = "audit_done")]
    AUDIT_DONE,

    [Display(Name = "pending_for_sap_cancellation")]
    PENDING_FOR_SAP_CANCELLATION,

    [Display(Name = "so_created")]
    SO_CREATED,

    [Display(Name = "pending_from_rh_approval")]
    PENDING_FROM_RH_APPROVAL,

    [Display(Name = "approved_from_rh")]
    APPROVED_FROM_RH,

    [Display(Name = "pending_from_logistics_approval")]
    PENDING_FROM_LOGISTICS_APPROVAL,

    [Display(Name = "pending_from_finance_approval")]
    PENDING_FROM_FINANCE_APPROVAL,

    [Display(Name = "pending_for_settlement")]
    PENDING_FOR_SETTLEMENT,

    [Display(Name = "settled")]
    SETTLED,

    [Display(Name = "pending_for_submission")]
    PENDING_FOR_SUBMISSION,

    [Display(Name = "claim_created")]
    CLAIM_CREATED,

    [Display(Name = "shipped")]
    SHIPPED,

    [Display(Name = "closed")]
    CLOSED,

    [Display(Name = "ready_to_dispatch")]
    READY_TO_DISPATCH
}
