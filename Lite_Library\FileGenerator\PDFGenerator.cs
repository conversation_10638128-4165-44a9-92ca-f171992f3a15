﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using FileGenerator.Attributes;
using FileGenerator.DataTableHelpers;
using FileGenerator.Interfaces;
using iText.IO.Image;
using iText.Kernel.Colors;
using iText.Kernel.Geom;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Draw;
using iText.Layout.Borders;
using iText.Layout.Element;
using iText.Layout.Properties;
using Attribute = System.Attribute;
using Document = iText.Layout.Document;

namespace FileGenerator;

public class PDFGenerator
{
    private readonly bool _useNomenclature;
    private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;

    public PDFGenerator(CompanyNomenclatureSpecifier nomenclatureSpecifier = null)
    {
        this.nomenclatureSpecifier = nomenclatureSpecifier;
        _useNomenclature = nomenclatureSpecifier != null;
    }

    public static Image AddCompanyLogo(long companyId)
    {
        var companyLogoURL = "https://manage.fieldassist.in/FieldAssistPOC/Company/GetCompanyLogoViaId?companyId=" + companyId;

        var imageData = ImageDataFactory.Create(companyLogoURL);
        var image = new Image(imageData);

        // Resize image depending on your need
        image.ScaleAbsolute(140f, 120f);

        // Set image alignment
        image.SetHorizontalAlignment(HorizontalAlignment.LEFT);

        return image;
    }

    public static Image AddFALogo()
    {
        var faLogoURL = "https://i0.wp.com/www.fieldassist.in/wp-content/uploads/2016/11/logo_1-1.png";

        var imageData = ImageDataFactory.Create(faLogoURL);
        var image = new Image(imageData);

        image.ScaleToFit(140f, 120f);
        image.SetMarginBottom(1f);
        image.SetHorizontalAlignment(HorizontalAlignment.RIGHT);

        return image;
    }

    public static Table CreateDataTable(DataTable data)
    {
        var table = new Table(data.Columns.Count);
        table.SetHorizontalAlignment(HorizontalAlignment.CENTER);
        table.SetVerticalAlignment(VerticalAlignment.MIDDLE);
        table.SetPadding(3);
        table.SetMarginTop(10f);
        table.SetMarginBottom(12.5f);
        table.SetWidth(UnitValue.CreatePercentValue(100f));

        foreach (DataColumn col in data.Columns)
        {
            table.AddCell(new Cell().Add(new Paragraph(col.ColumnName)).SetTextAlignment(TextAlignment.CENTER));
        }

        for (var i = 0; i < data.Rows.Count; i++)
        {
            for (var j = 0; j < data.Columns.Count; j++)
            {
                var dataValue = data.Rows[i][j];
                var cellValue = dataValue.ToString();

                if (data.Columns[j].DataType == typeof(decimal) || data.Columns[j].DataType == typeof(double))
                {
                    cellValue = Math.Round(Convert.ToDecimal(dataValue), 2).ToString();
                }

                var cell = new Cell().Add(new Paragraph(cellValue))
                    .SetTextAlignment(TextAlignment.CENTER)
                    .SetPadding(3);

                table.AddCell(cell);
            }
        }

        return table;
    }

    public static Table CreateDataTableWithOrdering(DataTable data, IEnumerable<PropertyInfo> columns)
    {
        var table = new Table(columns.Count());
        table.SetHorizontalAlignment(HorizontalAlignment.CENTER);
        table.SetVerticalAlignment(VerticalAlignment.MIDDLE);
        table.SetPadding(3);
        table.SetMarginTop(10f);
        table.SetMarginBottom(12.5f);
        table.SetWidth(UnitValue.CreatePercentValue(100f));
        foreach (var col in columns)
        {
            var Tattr = Attribute.GetCustomAttribute(col, typeof(TableFieldAttribute));
            if (Tattr is TableFieldAttribute excelAttrOfT)
            {
                table.AddCell(new Cell().Add(new Paragraph(excelAttrOfT.ColumnName)));
            }
        }

        for (var i = 0; i < data.Rows.Count; i++)
        {
            foreach (var col in columns)
            {
                table.AddCell(new Cell().Add(new Paragraph(data.Rows[i][col.Name].ToString())));
            }
        }

        return table;
    }

    public static Table CreateDataTable(DataTable data, IEnumerable<PropertyInfo> columns, List<string> columnsToDelete = null)
    {
        var table = new Table(UnitValue.CreatePercentArray(data.Columns.Count)).UseAllAvailableWidth();
        table.SetMarginTop(10f);
        table.SetMarginBottom(12.5f);

        foreach (DataColumn col in data.Columns)
        {
            if (!columnsToDelete.Contains(col.ColumnName))
            {
                var colProp = columns.ToList().Where(d => d.Name == col.ColumnName).Select(d => d).FirstOrDefault();
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT && excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                {
                    var cell = new Cell().Add(new Paragraph(excelAttrOfT.ColumnName)).SetTextAlignment(TextAlignment.CENTER).SetPadding(3);
                    table.AddCell(cell);
                }
            }
        }

        for (var i = 0; i < data.Rows.Count; i++)
        {
            for (var j = 0; j < data.Columns.Count; j++)
            {
                if (!columnsToDelete.Contains(data.Columns[j].ColumnName))
                {
                    var colProp = columns.ToList().Where(d => d.Name == data.Columns[j].ColumnName).Select(d => d).FirstOrDefault();
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT && excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                    {
                        var dataValue = data.Rows[i].ItemArray[j];
                        switch (excelAttrOfT.ColumnDataType)
                        {
                            case DataTypeAttribute.Date:
                                dataValue = ((DateTimeOffset)dataValue).ToString("dd/MM/yyyy");
                                break;
                            case DataTypeAttribute.Time:
                                dataValue = dataValue != DBNull.Value ? ((DateTimeOffset)dataValue).ToString(@"HH:mm") : "";
                                break;
                            case DataTypeAttribute.Decimal:
                                dataValue = Math.Round(Convert.ToDecimal(dataValue), 2).ToString();
                                break;
                            default:
                                dataValue = dataValue.ToString();
                                break;
                        }

                        var cell = new Cell().Add(new Paragraph(dataValue.ToString())).SetTextAlignment(TextAlignment.CENTER).SetPadding(3);
                        table.AddCell(cell);
                    }
                }
            }
        }

        return table;
    }

    public static Table CreateDataTableWithoutHeader(DataTable data, IEnumerable<PropertyInfo> columns, List<string> columnsToDelete = null)
    {
        var table = new Table(data.Columns.Count);
        table.SetWidth(UnitValue.CreatePercentValue(100)).UseAllAvailableWidth();
        table.SetMarginTop(10f);
        table.SetMarginBottom(12.5f);

        for (var i = 0; i < data.Rows.Count; i++)
        {
            for (var j = 0; j < data.Columns.Count; j++)
            {
                if (!columnsToDelete.Contains(data.Columns[j].ColumnName))
                {
                    var colProp = columns.ToList().Where(d => d.Name == data.Columns[j].ColumnName).Select(d => d)
                        .FirstOrDefault();
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                        {
                            if (excelAttrOfT.ColumnDataType == DataTypeAttribute.Date)
                            {
                                var dataValue = ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString("dd/MM/yyyy");
                                table.AddCell(new Cell().Add(new Paragraph(dataValue)));
                            }
                            else if (excelAttrOfT.ColumnDataType == DataTypeAttribute.Time)
                            {
                                var dataValue = data.Rows[i].ItemArray[j] != DBNull.Value
                                    ? ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString(@"HH:mm")
                                    : "";
                                table.AddCell(new Cell().Add(new Paragraph(dataValue)));
                            }
                            else
                            {
                                var dataValue = data.Rows[i].ItemArray[j].ToString();
                                table.AddCell(new Cell().Add(new Paragraph(dataValue)));
                            }
                        }
                    }
                }
            }
        }

        return table;
    }

    public static List<Table> CreateFlatDataTable<T, T1>(T data, long companyId, string sheetName,
        IEnumerable<T1> dataforsec, int? noOfColumnsInInfoTable, List<string> columnsToDelete = null)
    {
        var toret = new List<Table>();
        var columns = data.GetType().GetProperties()
            .Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);

        var item = data;
        foreach (var colProp in columns)
        {
            if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var dataValue = colProp.GetValue(item) ?? "";
                    if (excelAttrOfT.PDFColCategory == PDFColCategory.Info && (dataValue.ToString() != "" ||
                                                                               excelAttrOfT.ColumnRequirement == Requirement.Required))
                    {
                        var table = new Table(noOfColumnsInInfoTable ?? 12);
                        table.SetWidth(UnitValue.CreatePercentValue(30)).UseAllAvailableWidth();
                        table.SetHorizontalAlignment(HorizontalAlignment.LEFT);

                        var headerCell = new Cell()
                            .Add(new Paragraph(excelAttrOfT.ColumnName))
                            .SetHorizontalAlignment(HorizontalAlignment.LEFT)
                            .SetVerticalAlignment(VerticalAlignment.MIDDLE)
                            .SetPadding(3)
                            .SetBorder(Border.NO_BORDER);
                        table.AddCell(headerCell);

                        var dataCell = new Cell()
                            .Add(new Paragraph(dataValue.ToString()))
                            .SetHorizontalAlignment(HorizontalAlignment.LEFT)
                            .SetVerticalAlignment(VerticalAlignment.MIDDLE)
                            .SetPadding(3)
                            .SetBorder(Border.NO_BORDER);
                        table.AddCell(dataCell);

                        toret.Add(table);
                    }
                    else if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                    {
                        var ConvertToDataTable = new ListToDataTableConverter();
                        if (dataValue.ToString() != "")
                        {
                            var result = (IEnumerable<T1>)dataValue;
                            var columnsforsec = result.FirstOrDefault()?.GetType().GetProperties().Where(p =>
                                Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                            var dt = ListToDataTableConverter.GetDataTableFromList(result, sheetName);
                            dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                            var pdfTableFlat = CreateDataTable(dt, columnsforsec, columnsToDelete);
                            toret.Add(pdfTableFlat);
                        }
                    }
                }
            }
        }

        return toret;
    }

    public static List<Table> CreateFlatTotalTable<T>(long companyId, T data, List<string> columnsToDelete)
    {
        if (data == null)
        {
            return null;
        }

        var columns = data?.GetType().GetProperties()
            .Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
        var ConvertToDataTable = new ListToDataTableConverter();
        var list = new List<T>();
        list.Add(data);
        var dt = ListToDataTableConverter.GetDataTableFromList(list, "Aggregates");
        dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
        var totalTable = CreateFlatDataTable(data, companyId, "", new List<string>(), 2, columnsToDelete);
        return totalTable;
    }

    public static Table CreateInfoTable<T>(long companyId, IEnumerable<T> data, int? noOfColumnsInInfoTable, List<string> columnsToDelete = null)
    {
        var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p =>
            Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
        var firstrowofdata = data.Take(1);
        var table = new Table(noOfColumnsInInfoTable ?? 12);
        table.SetMarginTop(10f);
        table.SetMarginBottom(12.5f);

        foreach (var item in firstrowofdata)
        {
            foreach (var colProp in columns)
            {
                if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        var dataValue = colProp.GetValue(item) ?? "";
                        if (excelAttrOfT.PDFColCategory == PDFColCategory.Info)
                        {
                            var headerCell = new Cell()
                                .Add(new Paragraph(excelAttrOfT.ColumnName))
                                .SetBackgroundColor(new DeviceRgb(211, 211, 211))
                                .SetHorizontalAlignment(HorizontalAlignment.CENTER)
                                .SetVerticalAlignment(VerticalAlignment.MIDDLE)
                                .SetPadding(3)
                                .SetBorder(Border.NO_BORDER);
                            table.AddCell(headerCell);

                            var dataCell = new Cell()
                                .Add(new Paragraph(dataValue.ToString()))
                                .SetBackgroundColor(new DeviceRgb(255, 255, 255))
                                .SetHorizontalAlignment(HorizontalAlignment.CENTER)
                                .SetVerticalAlignment(VerticalAlignment.MIDDLE)
                                .SetPadding(3)
                                .SetBorder(Border.NO_BORDER);
                            table.AddCell(dataCell);
                        }
                    }
                }
            }
        }

        return table;
    }

    public static Table CreateTotalTable<T>(long companyId, T data, List<string> columnsToDelete)
    {
        if (data == null)
        {
            return null;
        }

        var columns = data?.GetType().GetProperties()
            .Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
        var ConvertToDataTable = new ListToDataTableConverter();
        var list = new List<T>();
        list.Add(data);
        var dt = ListToDataTableConverter.GetDataTableFromList(list, "Aggregates");
        dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
        var totalTable = CreateDataTableWithoutHeader(dt, columns, columnsToDelete);
        return totalTable;
    }

    public static void MakePDFTADA<T1, T2>(Stream output, List<Paragraph> Headers, IEnumerable<T1> dataForTable1, IEnumerable<IEnumerable<T2>> dataFornextPages = null, List<List<Paragraph>> HeadersFornextPages = null)
    {
        var pdf = new PdfDocument(new PdfWriter(output));
        pdf.SetDefaultPageSize(PageSize.A2);
        var document = new Document(pdf);
        document.SetMargins(50, 50, 25, 25);
        var pagesToRemove = new List<int>();

        //Page1
        foreach (var item in Headers)
        {
            document.Add(item);
        }

        if (dataForTable1.Count() > 0)
        {
            var cols = dataForTable1.FirstOrDefault().GetType().GetProperties().Where(p =>
                Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            var ConvertToDataTable = new ListToDataTableConverter();
            var dt = ListToDataTableConverter.GetDataTableFromList(dataForTable1, "Table1");
            var pdfTableFlat = CreateDataTableWithOrdering(dt, cols);
            document.Add(pdfTableFlat);
        }

        if (Headers.Count() == 0 && dataForTable1.Count() == 0)
        {
            pagesToRemove.Add(1);
        }

        //next Pages
        if (dataFornextPages.Count() > 0)
        {
            var dataFornextPagesList = dataFornextPages.ToList();
            var columns = dataFornextPages.Where(x => x.Count() > 0).FirstOrDefault().FirstOrDefault().GetType().GetProperties().Where(p =>
                Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            for (var i = 0; i < dataFornextPagesList.Count(); i++)
            {
                var currentPageNumber = i + 2;
                document.Add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                foreach (var item in HeadersFornextPages[i])
                {
                    document.Add(item);
                }

                if (dataFornextPagesList[i].Count() > 0)
                {
                    var ConvertToDataTable = new ListToDataTableConverter();
                    var dt = ListToDataTableConverter.GetDataTableFromList(dataFornextPagesList[i], "table");
                    var pdfTableFlat = CreateDataTableWithOrdering(dt, columns);
                    document.Add(pdfTableFlat);
                }

                //remove page if empty
                if (HeadersFornextPages[i].Count() == 0 && dataFornextPagesList[i].Count() == 0)
                {
                    pagesToRemove.Add(currentPageNumber);
                }
            }
        }

        //remove emptyPages
        foreach (var pageNo in pagesToRemove)
        {
            pdf.RemovePage(pageNo);
        }

        var emptyLastPageNos = pdf.GetNumberOfPages();
        document.Close();
    }

    public static void MakeMultipleDataPDF<T1, T2>(long companyId, Stream output, string sheetName, IEnumerable<T1> data,
        IEnumerable<T2> dataForSecTable, int? noOfColumnsInInfoTable, Paragraph singnature = null,
        string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null,
        List<Table> TotalTable = null)
    {
        if (data == null || data.Count() == 0)
        {
            var pdf = new PdfDocument(new PdfWriter(output));
            pdf.SetDefaultPageSize(PageSize.A2);
            var document = new Document(pdf);
            document.SetMargins(50, 50, 25, 25);
            var message = new Paragraph("Day not started by User Or Preferences not set").SetTextAlignment(TextAlignment.CENTER);
            document.Add(message);
            document.Close();
        }
        else
        {
            var pdf = new PdfDocument(new PdfWriter(output));
            pdf.SetDefaultPageSize(PageSize.A2);
            var document = new Document(pdf);
            document.SetMargins(50, 50, 25, 25);
            document.Add(AddCompanyLogo(companyId));
            foreach (var d in data)
            {
                var pdfTableFlat = CreateFlatDataTable(d, companyId, sheetName, dataForSecTable,
                    noOfColumnsInInfoTable, columnsToDelete);
                foreach (var pdf1 in pdfTableFlat)
                {
                    document.Add(pdf1);
                }

                document.Add(new Paragraph(" "));
                var line2 = new LineSeparator(new SolidLine(1f)).SetMarginTop(100).SetMarginBottom(1);
                document.Add(line2);
            }

            document.Add(new Paragraph(" "));
            if (TotalTable != null && TotalTable.Count() > 0)
            {
                foreach (var pdf1 in TotalTable)
                {
                    document.Add(pdf1);
                }
            }

            if (singnature != null)
            {
                singnature.SetTextAlignment(TextAlignment.LEFT);
                document.Add(singnature);
            }

            var asmPara = new Paragraph()
                .SetTextAlignment(TextAlignment.CENTER);
            document.Add(asmPara);
            document.Close();
        }
    }

    public void MakePDF<T1>(long companyId, Stream output, string sheetName, IEnumerable<T1> data,
        IEnumerable<T1> dataForSecTable, int? noOfColumnsInInfoTable, Paragraph singnature = null,
        string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null,
        Table TotalTable = null)
    {
        var pdfDoc = new PdfDocument(new PdfWriter(output));
        var document = new Document(pdfDoc, PageSize.A2.Rotate());
        document.SetMargins(50, 50, 25, 25);

        if (data == null || data.Count() == 0)
        {
            var message = new Paragraph("Day not started by User Or Preferences not set")
                .SetTextAlignment(TextAlignment.CENTER);
            document.Add(message);
        }
        else
        {
            var pivotCreater = new PivotCreater();

            var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p =>
                Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            var Table1 = CreateInfoTable(companyId, data, noOfColumnsInInfoTable, columnsToDelete);

            document.Add(AddCompanyLogo(companyId));
            document.Add(Table1);

            if (dataForSecTable.Count() > 0)
            {
                var ConvertToDataTable = new ListToDataTableConverter();
                var dt = ListToDataTableConverter.GetDataTableFromList(dataForSecTable, sheetName);

                if (GroupColumn != null)
                {
                    dt = RemoveColumnsAndChangeHeader(companyId, columns, dt, columnsToDelete);
                    var pivotDt = PivotCreater.Pivot(dt, dt.Columns[GroupColumn], dt.Columns[ValueColumn]);
                    var pdfTableForPivto = CreateDataTable(pivotDt);
                    document.Add(pdfTableForPivto);
                }
                else
                {
                    dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
                    var pdfTableFlat = CreateDataTable(dt, columns, columnsToDelete);
                    document.Add(pdfTableFlat);
                }

                if (TotalTable != null)
                {
                    document.Add(TotalTable);
                }
            }
            else
            {
                var nosale = new Paragraph("User have not booked any order")
                    .SetTextAlignment(TextAlignment.CENTER);
                document.Add(nosale);
            }

            if (singnature != null)
            {
                singnature.SetTextAlignment(TextAlignment.LEFT);
                document.Add(singnature);
            }

            var asmPara = new Paragraph()
                .SetTextAlignment(TextAlignment.CENTER);
            document.Add(asmPara);
        }

        document.Close();
    }

    public static DataTable RemoveColumns(long companyId, IEnumerable<PropertyInfo> columns, DataTable dt,
        List<string> columnsToDelete)
    {
        var ordinal = 0;
        foreach (var colProp in columns)
        {
            if (columnsToDelete != null && columnsToDelete.Contains(colProp.Name))
            {
                dt.Columns.Remove(colProp.Name);
            }
            else
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    if (excelAttrOfT.PDFColCategory != PDFColCategory.Table)
                    {
                        dt.Columns.Remove(colProp.Name);
                    }
                    else
                    {
                        dt.Columns[colProp.Name].SetOrdinal(ordinal++);
                    }
                }
            }
        }

        return dt;
    }

    public DataTable RemoveColumnsAndChangeHeader(long companyId, IEnumerable<PropertyInfo> columns, DataTable dt,
        List<string> columnsToDelete)
    {
        var ordinal = 0;
        foreach (var colProp in columns)
        {
            if (columnsToDelete != null && columnsToDelete.Contains(colProp.Name))
            {
                dt.Columns.Remove(colProp.Name);
            }
            else
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var Tcolumnname = excelAttrOfT.ColumnName;

                    if (excelAttrOfT.PDFColCategory != PDFColCategory.Table)
                    {
                        dt.Columns.Remove(colProp.Name);
                    }
                    else
                    {
                        if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                        {
                            var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                            listforheader.Remove(listforheader[0]);
                            var striingotherthennomenclature = string.Join(" ", listforheader);
                            Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;
                            dt.Columns[colProp.Name].SetOrdinal(ordinal);
                            dt.Columns[colProp.Name].ColumnName = Tcolumnname;
                        }
                        else
                        {
                            dt.Columns[colProp.Name].SetOrdinal(ordinal);
                            dt.Columns[colProp.Name].ColumnName = excelAttrOfT.ColumnName;
                        }

                        ordinal++;
                    }
                }
            }
        }

        return dt;
    }
}
