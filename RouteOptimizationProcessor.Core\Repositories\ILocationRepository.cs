﻿using Libraries.CommonEnums;
using RouteOptimizationProcessor.Core.Models.CoreModels;


namespace RouteOptimizationProcessor.Core.Repositories;

public interface ILocationRepository
{
    Task<List<Location>> GetOutletsFromRoutesAsync(List<long> routes, long companyId);

    Task<List<Location>> GetOutletsFromBeatsAsync(long companyId, List<long> beatIds);

    Task<Dictionary<OutletSegmentation, List<long>>> GetSegementionEnumsForIds(List<long> segmentationIds, long companyId);
}
