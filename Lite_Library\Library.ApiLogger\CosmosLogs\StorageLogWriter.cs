﻿using System;
using System.Threading.Tasks;
using Library.ApiLogger.Interface;
using Library.StorageWriter;

namespace Library.ApiLogger.CosmosLogs;

public class StorageLogWriter : ISecondaryLogWriter, IDisposable
{
    private readonly BlobWriter _logWriter;

    public StorageLogWriter(string storageConnectionString, string containerName)
    {
        _logWriter = new BlobWriter(storageConnectionString, containerName);
    }

    public void Dispose()
    {
        //Do Nothing
    }

    public string GetPublicUrl(string fileName)
    {
        return $"{_logWriter.GetPublicPath(fileName)}.json";
    }

    public async Task Log(IApiLog request)
    {
        await LogToBlob(request.RequestId, request).ConfigureAwait(false);
    }

    public async Task LogToBlob<T>(string id, T request)
    {
        await _logWriter.WriteToBlob(id + ".json", request, true).ConfigureAwait(false);
    }
}
