﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class TrendReportNLTPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public TrendReportNLTPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        // Name Property Needs to match the key used for Nomenclature For QuickViz
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager,
            Name = "GSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.Employee,
            Name = "ESM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser HQ",
            Name = "UserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser ErpId",
            Name = "UserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Designation",
            Name = "UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "User Status",
            Name = "UserStatus",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Active Days",
            Name = "ActiveDays",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Region",
            Name = "FieldUserRegion",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Zone",
            Name = "FieldUserZone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },

        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work

        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position,
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position,
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position,
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position,
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position,
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position,
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position,
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position,
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = "Field User"
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = "Field User"
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "User Status",
            Name = "L1Position_UserStatus",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = "Field User"
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Active Days",
            Name = "L1Position_ActiveDays",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = "Field User"
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser Region",
            Name = "L1Position_UserRegion",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = "Field User"
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser Zone",
            Name = "L1Position_UserZone",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = "Field User"
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText1,
            Name = "EmployeeAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText2,
            Name = "EmployeeAttributeText2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber1,
            Name = "EmployeeAttributeNumber1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber2,
            Name = "EmployeeAttributeNumber2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Date Of Joining",
            Name = "DateOfJoining",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Email Id",
            Name = "EmailId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "User Type",
            Name = "UserType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Contact Number",
            Name = "ContactNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level7,
            Name = "Level7",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level6,
            Name = "Level6",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level5,
            Name = "Level5",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Territory,
            Name = "Territory",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "TC",
            Name = "TC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "PC",
            Name = "PC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg. TC (per day)",
            Name = "AvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg. PC (per day)",
            Name = "AvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.UTC,
            Name = "UTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.UPC,
            Name = "UPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "OVT",
            Name = "OVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "OVC",
            Name = "OVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Days Started Late",
            Name = "DaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Days Frst Call OVC",
            Name = "DaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg Retailing Time",
            PerspectiveType = PerspectiveType.DayStart,
            Name = "AvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },

        //new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Scheme Value"                     , Name = "SchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     , TimeFrames = new List<NLTFrames>{ NLTFrames.MTD, NLTFrames.LMTD, NLTFrames.LM, NLTFrames.L3MAvg, NLTFrames.PerL3M } },
        //new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Number of telephonic orders"      , Name = "Numberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     , TimeFrames = new List<NLTFrames>{ NLTFrames.MTD, NLTFrames.LMTD, NLTFrames.LM, NLTFrames.L3MAvg, NLTFrames.PerL3M } },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.Unit})",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Value",
            Name = "Value",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "NetValue",
            Name = "NetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.StdUnit})",
            Name = "SalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.SuperUnit})",
            Name = "SalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Net Value (Dispatch)",
            Name = "DispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Dispatch Qty ({linkNames.StdUnit})",
            Name = "DispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Dispatch Qty ({linkNames.SuperUnit})",
            Name = "DispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Dispatch Qty ({linkNames.Unit})",
            Name = "DispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.AvgValuePerCall,
            Name = "DropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Lines",
            Name = "TotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        //new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Unique Lines"               , Name = "TotalUniqueLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     , TimeFrames = new List<NLTFrames>{ NLTFrames.MTD, NLTFrames.LMTD, NLTFrames.LM, NLTFrames.L3MAvg, NLTFrames.PerL3M } },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "LPC",
            Name = "LPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Ach",
            Name = "Achievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Ach(Dispatch)",
            Name = "DispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Days Retailing",
            Name = "DaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "SC",
            Name = "SC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "USC",
            Name = "USC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "New Outlets",
            Name = "NewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "New Outlets Value",
            Name = "NewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg Value Per New Outlet",
            Name = "ValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"FO Qty ({linkNames.Unit})",
            Name = "FOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"FO Qty ({linkNames.StdUnit})",
            Name = "FOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "FO Value",
            Name = "FOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.FocusedOutlet + " " + linkNames.UTC,
            Name = "FocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.FocusedOutlet + " " + linkNames.UPC,
            Name = "FocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Focus Product Order Qty ({linkNames.Unit})",
            Name = "FocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Focus Product Order Qty ({linkNames.StdUnit})",
            Name = "FocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Focus Product Order Qty ({linkNames.SuperUnit})",
            Name = "FocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Working Days",
            Name = "WorkingDays",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            TimeFrames = new List<NLTFrames>
            {
                NLTFrames.MTD,
                NLTFrames.LMTD,
                NLTFrames.LM,
                NLTFrames.L3MAvg,
                NLTFrames.PerL3M,
                NLTFrames.PerGrowth
            }
        },

        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Employee Overall Target",
            Name = "Targets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Total Outlets",
            Name = "Outlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Total Beats",
            Name = "Beats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Total Routes",
            Name = "Routes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Total Distributors",
            Name = "Distributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = linkNames.FocusedOutlet + " " + "Count",
            Name = "TotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Focused Outlet Target",
            Name = "FocusedOutletTarget",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        }
    };
}
