﻿using System;

namespace Library.EmailService;

public static class EmailTemplates
{
    private static string AnchorUrlTemplate(string actionText, string action_url)
    {
        var template =
            @"<table class=""body-action"" align=""center"" width=""100%"" cellpadding=""0"" cellspacing=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin: 30px auto; padding: 0; text-align: center; width: 100%;"">
                                            <tr>
                                                <td align =""center"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; word-break: break-word;"">

                                                    <table width =""100%"" border=""0"" cellspacing=""0"" cellpadding=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;"">
                                                        <tr>
                                                            <td align =""center"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; word-break: break-word;"">
                                                                <table border =""0"" cellspacing=""0"" cellpadding=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;"">
                                                                    <tr>
                                                                        <td style =""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; word-break: break-word;"">
                                                                            <a href =""{{action_url}}"" class=""button button--green"" target=""_blank"" style=""-webkit-text-size-adjust: none; background: #22BC66; border-color: #22bc66; border-radius: 3px; border-style: solid; border-width: 10px 18px; box-shadow: 0 2px 3px rgba(0, 0, 0, 0.16); box-sizing: border-box; color: #FFF; display: inline-block; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; text-decoration: none;"">{{actionText}}</a>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>";
        template = template.Replace("{{actionText}}", actionText);
        template = template.Replace("{{action_url}}", action_url);
        return template;
    }

    private static string BaseTemplate(string header, string mainMessage, string reasonToContactSupport,
        string supportURL, string name, string actionHtml, string urlHtml, string innerWidth = "570px",
        string tabularData = null)
    {
        mainMessage = mainMessage.Replace("<b>",
            @"<strong style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;"">");
        mainMessage = mainMessage.Replace("</b>", @"</strong>");

        reasonToContactSupport = reasonToContactSupport.Replace("<b>",
            @"<strong style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;"">");
        reasonToContactSupport = reasonToContactSupport.Replace("</b>", @"</strong>");

        var template =
            @"<!DOCTYPE html PUBLIC "" -//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">
<html xmlns=""http://www.w3.org/1999/xhtml"" xmlns=""http://www.w3.org/1999/xhtml"">
<head>
    <meta name =""viewport"" content=""width=device-width, initial-scale=1.0"" />
    <meta http-equiv=""Content-Type"" content=""text/html; charset=UTF-8"" />
    <title>FieldAssist - {{header}}</title></head>
<body style =""-webkit-text-size-adjust: none; box-sizing: border-box; color: #74787E; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; height: 100%; line-height: 1.4; margin: 0; width: 100% !important;"" bgcolor=""#F2F4F6"">
    <style type =""text/css"">
        body {
            width: 100 % !important;
            height: 100 %;
            margin: 0;
            line - height: 1.4;
            background - color: #F2F4F6;
            color: #74787E;
            -webkit - text - size - adjust: none;
        }
.tabular-data-table th, .tabular-data-table td{
    padding: 2px 10px;
    border: 1px solid;
}
        @media only screen and(max-width: 600px)
        {
            .email - body_inner {
                width: 100 % !important;
            }

            .email - footer {
                width: 100 % !important;
            }
        }

        @media only screen and(max-width: 500px)
        {
            .button {
                width: 100 % !important;
            }
        }
    </style>
    <span class=""preheader"" style=""box-sizing: border-box; display: none !important; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 1px; line-height: 1px; max-height: 0; max-width: 0; mso-hide: all; opacity: 0; overflow: hidden; visibility: hidden;"">{{header}}</span>
    <table class=""email-wrapper"" width=""100%"" cellpadding=""0"" cellspacing=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin: 0; padding: 0; width: 100%;"" bgcolor=""#F2F4F6"">
        <tr>
            <td align =""center"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; word-break: break-word;"">
                <table class=""email-content"" width=""100%"" cellpadding=""0"" cellspacing=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin: 0; padding: 0; width: 100%;"">
                    <tr>
                        <td class=""email-masthead"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; padding: 25px 0; word-break: break-word;"" align=""center"">
                            <a href =""https://www.fieldassist.in"" class=""email-masthead_name"" style=""box-sizing: border-box; color: #bbbfc3; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 16px; font-weight: bold; text-decoration: none; text-shadow: 0 1px 0 white;"">
                                <img height='50px' src='https://manage.fieldassist.in/Content/Logo_3.png' />
                            </a>
                        </td>
                    </tr>

                    <tr>
                        <td class=""email-body"" width=""100%"" cellpadding=""0"" cellspacing=""0"" style=""-premailer-cellpadding: 0; -premailer-cellspacing: 0; border-bottom-color: #EDEFF2; border-bottom-style: solid; border-bottom-width: 1px; border-top-color: #EDEFF2; border-top-style: solid; border-top-width: 1px; box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin: 0; padding: 0; width: 100%; word-break: break-word;"" bgcolor=""#FFFFFF"">
                             <table class=""email-body_inner"" align=""center"" cellpadding=""0"" cellspacing=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin: 0 auto; padding: 0; width: {{innerWidth}};"" bgcolor=""#FFFFFF"">

                                <tr>
                                    <td class=""content-cell"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; padding: 35px; word-break: break-word;"">
                                        <h1 style =""box-sizing: border-box; color: #2F3133; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 19px; font-weight: bold; margin-top: 0;"" align=""left"">Hi {{name}},</h1>
                                        <p style =""box-sizing: border-box; color: #74787E; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 16px; line-height: 1.5em; margin-top: 0;"" align=""left"">{{mainMessage}}</p>
                                        {{tabularData}}

                                        {{actionHtml}}                                        <p style =""box-sizing: border-box; color: #74787E; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 16px; line-height: 1.5em; margin-top: 0;"" align=""left"">Please <a href=""{{supportURL}}"" style=""box-sizing: border-box; color: #3869D4; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;"">contact support</a>, {{reasonToContactSupport}}</p>
                                        <p style =""box-sizing: border-box; color: #74787E; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 16px; line-height: 1.5em; margin-top: 0;"" align=""left"">
                                            Thanks,
                                            <br />Team FieldAssist
                                        </p>                                        {{urlHtml}}                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td style =""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; word-break: break-word;"">
                            <table class=""email-footer"" align=""center"" width=""570"" cellpadding=""0"" cellspacing=""0"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin: 0 auto; padding: 0; text-align: center; width: 570px;"">
                                <tr>
                                    <td class=""content-cell"" align=""center"" style=""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; padding: 35px; word-break: break-word;"">
                                        <p class=""sub align-center"" style=""box-sizing: border-box; color: #AEAEAE; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 12px; line-height: 1.5em; margin-top: 0;"" align=""center"">© 2018 FieldAssist. All rights reserved.</p>
                                        <p class=""sub align-center"" style=""box-sizing: border-box; color: #AEAEAE; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 12px; line-height: 1.5em; margin-top: 0;"" align=""center"">
                                            Flick2Know Technologies
                                            <br />241-242, Tower B3, Spaze IT Park
                                            <br /> Sector 49, Sohna Road, Gurgaon IN
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>";
        template = template.Replace("{{header}}", header);
        template = template.Replace("{{mainMessage}}", mainMessage);
        template = template.Replace("{{tabularData}}", string.IsNullOrWhiteSpace(tabularData) ? "" : tabularData);
        template = template.Replace("{{supportURL}}", supportURL);
        template = template.Replace("{{reasonToContactSupport}}", reasonToContactSupport);
        template = template.Replace("{{name}}", name);
        template = template.Replace("{{actionHtml}}", actionHtml);
        template = template.Replace("{{urlHtml}}", urlHtml);
        template = template.Replace("{{innerWidth}}", innerWidth);

        return template;
    }

    private static string UrlToCopyTemplate(string action_url)
    {
        var template =
            @"<table class=""body-sub"" style=""border-top-color: #EDEFF2; border-top-style: solid; border-top-width: 1px; box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; margin-top: 25px; padding-top: 25px;"">
                                            <tr>
                                                <td style =""box-sizing: border-box; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; word-break: break-word;"">
                                                    <p class=""sub"" style=""box-sizing: border-box; color: #74787E; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 12px; line-height: 1.5em; margin-top: 0;"" align=""left"">If you’re having trouble with the button above, copy and paste the URL below into your web browser.</p>
                                                    <p class=""sub"" style=""box-sizing: border-box; color: #74787E; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 12px; line-height: 1.5em; margin-top: 0;"" align=""left"">
                                                        <a href=""{{action_url}}"">
                                                            {{action_url}}
                                                        </a>
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>";
        template = template.Replace("{{action_url}}", action_url);
        return template;
    }

    public static string AdvanceLeaveApprovalRequest(string outputLink, string ManagerName, string RequesterName, string startDate, string endDate, string reason)
    {
        var reasonToContactSupport = "if you are facing any problem in viewing the request or if you think this is a mistake.";
        var msg = $"{RequesterName} has submitted an Advance Leave for the dates {startDate} - {endDate} with reason as {reason}. Please login FieldAssist dashboard for taking action.\n" +
                  $"Note : Once approved, the leave will be taken into consideration from Next Day.";
        var supportUriBuilder = new UriBuilder($"mailto:<EMAIL>?subject=Advance Leave Approval of {RequesterName}&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;

        var outputUriBuilder = new UriBuilder(outputLink);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);
        var actionHtml = AnchorUrlTemplate("View Request", outputUriBuilder.Uri.AbsoluteUri);

        var template = BaseTemplate($"Advance Leave Approval of {RequesterName}", msg, reasonToContactSupport, supportURL, ManagerName, actionHtml, urlHtml);
        return template;
    }

    public static string ApproveTourPlan(string outputLink, string ManagerName, string RequesterName)
    {
        var reasonToContactSupport = "if you are facing any problem in viewing the request or if you think this is a mistake.";
        var msg = $"{RequesterName} has submitted a Tour Plan request. Kindly click on the button below to view the request. You may be required to login at FieldAssist Dashboard.";
        var supportUriBuilder = new UriBuilder($"mailto:<EMAIL>?subject=Issue in TourPlan Approval of {RequesterName}&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("View Tour Plan", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);
        var template = BaseTemplate($"Tour Plan Request of {RequesterName}", msg, reasonToContactSupport, supportURL, ManagerName, actionHtml, urlHtml);
        return template;
    }

    public static string EmployeeDSRLink(string outputLink, string UserName)
    {
        var reasonToContactSupport = "if you are facing any problem in downloading the report or you think this is a mistake.";
        var msg = "Here's the link for the DSR Report";
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Report&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;

        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("Download Report", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);

        var template = BaseTemplate("Requested DSR Report", msg, reasonToContactSupport, supportURL, UserName, actionHtml, urlHtml);
        return template;
    }

    public static string EmployeeTADAClaimFormLink(string outputLink, string UserName, DateTime startDate, DateTime endDate)
    {
        var reasonToContactSupport =
            "if you are facing difficulty in viewing the report or if you think this mail is not relevant to you.";
        var msg = $"Kindly find your TADA claim form for {startDate:dd/MM/yyyy} to {endDate:dd/MM/yyyy} attached in the mail.";
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in TADA claim report&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;

        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("Download Report", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);

        var template = BaseTemplate("Requested TADA Claim Form", msg, reasonToContactSupport, supportURL, UserName,
            actionHtml, urlHtml);
        return template;
    }

    public static string OTPFOrBeatChange(string managerName, string SelectedBeat, string AssignBeat, string employeeName, string randomOTP)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg = $"{employeeName} has requested a change in Beat from {AssignBeat} to {SelectedBeat}. One Time Password (OTP) for such request is as Below. Please convey the OTP to '{employeeName}'  if you wish to approve the same";
        ;
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for Beat deviation", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string OTPFOrBeatChangeWithReason(string managerName, string SelectedBeat, string AssignBeat, string employeeName, string randomOTP, string reason)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg =
            $"{employeeName} has request a change in Beat from {AssignBeat} to {SelectedBeat} Reason for Beat change is  '{reason}'. OTP for such request is '{randomOTP}. Please convey the OTP to '{employeeName}' if wish to approve the same.";
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for Beat deviation", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string OTPForJourneyDiversion(string managerName, string Entity, string SelectedEntity, string AssignEntity, string employeeName, string randomOTP)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg = $"{employeeName} has requested a change in {Entity} from {AssignEntity} to {SelectedEntity}. One Time Password (OTP) for such request is as Below. Please convey the OTP to '{employeeName}'  if you wish to approve the same";
        ;
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for Beat deviation", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string OTPForJourneyDiversionWithReason(string managerName, string Entity, string SelectedBeat, string AssignBeat, string employeeName, string randomOTP, string reason)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg =
            $"{employeeName} has request a change in {Entity} from {AssignBeat} to {SelectedBeat} Reason for {Entity} change is  '{reason}'. OTP for such request is '{randomOTP}. Please convey the OTP to '{employeeName}' if wish to approve the same.";
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for Beat deviation", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string OTPFOrRouteChange(string managerName, string SelectedRoute, string AssigndRoute, string employeeName, string randomOTP)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg = $"{employeeName} has requested a change in Route from {AssigndRoute} to {SelectedRoute}. One Time Password (OTP) for such request is as Below. Please convey the OTP to '{employeeName}'  if you wish to approve the same";
        ;
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for Route deviation", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string OTPFOrRouteChangeWithReason(string managerName, string SelectedRoute, string AssigndRoute, string employeeName, string randomOTP, string reason)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg =
            $"{employeeName} has requested a change in Route from {AssigndRoute} to {SelectedRoute} beacuse of '{reason}'. One Time Password (OTP) for such request is as Below. Please convey the OTP to '{employeeName}'  if you wish to approve the same";
        ;
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for Route deviation", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string OTPFOrValidationExtension(string managerName, string employeeName, string randomOTP)
    {
        var reasonToContactSupport = "if you are facing any issue.";
        var msg = $"{employeeName} has request an extension for validation days'. OTP for such request is '{randomOTP}. Please convey the OTP to '{employeeName}' if wish to approve the same.";
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Register Link&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var actionHtml = AnchorUrlTemplate(randomOTP, "");
        var urlHtml = UrlToCopyTemplate("");
        var template = BaseTemplate("OTP for day extension", msg, reasonToContactSupport, supportURL, managerName, actionHtml, urlHtml);
        return template;
    }

    public static string ReportRequestLinkForMailer(string outputLink, string UserName, string company,
        DateTime startDate, DateTime endDate, string reportType, DateTime requestDate, string status, string table)
    {
        var date_string = requestDate.ToString("dd-MMM-yyyy");
        var dateRange = $"{startDate.Date.ToString("dd-MMM-yyyy")} to {endDate.Date.ToString("dd-MMM-yyyy")}";
        var reasonToContactSupport =
            "if you are facing any problem in downloading the report or you think this is a mistake.";
        var msg = $"Here is the Report <b>'{reportType}'</b> for <b>{dateRange}</b>.";

        var supportUriBuilder = new UriBuilder($"mailto:<EMAIL>?subject=Issue in Report ({reportType}) dated {date_string} of {company}&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;

        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("Open Report", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);

        var template = BaseTemplate($"Report ({reportType}) for {dateRange}", msg, reasonToContactSupport,
            supportURL, UserName, actionHtml, urlHtml, tabularData: table);
        return template;
    }

    public static string ReportRequestLink(string outputLink, string UserName, string company, DateTime startDate, DateTime endDate, string reportType, DateTime requestDate, string status)
    {
        var date_string = requestDate.ToString("dd-MMM-yyyy");
        var dateRange = $"{startDate.Date:dd-MMM-yyyy} to {endDate.Date:dd-MMM-yyyy}";
        var reasonToContactSupport = "if you are facing any problem in downloading the report or you think this is a mistake.";
        var msg = $"Here's the link for the Report <b>'{reportType}'</b> requested on <b>{date_string}</b> for <b>{dateRange}</b>.";

        var supportUriBuilder = new UriBuilder($"mailto:<EMAIL>?subject=Issue in Report ({reportType}) dated {date_string} of {company}&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;

        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("Download Report", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);

        var template = BaseTemplate($"Requested Report ({reportType}) for {date_string}", msg, reasonToContactSupport, supportURL, UserName, actionHtml, urlHtml);
        return template;
    }

    public static string ReviewDistributorAdditionRequest(string outputLink, string ManagerName, string RequesterName, string outletNomeclature)
    {
        var reasonToContactSupport = "if you are facing any problem in viewing the request or if you think this is a mistake.";
        var msg = $"{RequesterName} has submitted a {outletNomeclature} addition request. Kindly click on the button below to view the request. You may be required to login at FieldAssist Dashboard.";
        var supportUriBuilder = new UriBuilder($"mailto:<EMAIL>?subject=Issue in Distributor addition Approval of {RequesterName}&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("View Request", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);
        var template = BaseTemplate($"{outletNomeclature} Addition Request of Distributor of {RequesterName}", msg, reasonToContactSupport, supportURL, ManagerName, actionHtml, urlHtml);
        return template;
    }

    public static string ReviewOutletAdditionRequest(string outputLink, string ManagerName, string RequesterName, string outletNomeclature)
    {
        var reasonToContactSupport = "if you are facing any problem in viewing the request or if you think this is a mistake.";
        var msg = $"{RequesterName} has submitted a {outletNomeclature} addition request. Kindly click on the button below to view the request. You may be required to login at FieldAssist Dashboard.";
        var supportUriBuilder = new UriBuilder($"mailto:<EMAIL>?subject=Issue in TourPlan Approval of {RequesterName}&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("View Request", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);
        var template = BaseTemplate($"{outletNomeclature} Addition RequestTour Plan Request of {RequesterName}", msg, reasonToContactSupport, supportURL, ManagerName, actionHtml, urlHtml);
        return template;
    }

    public static string ShareOrderEmail(string outputLink, string UserName)
    {
        var reasonToContactSupport = "if you are facing any problem in downloading the report or you think this is a mistake.";
        var msg = "Here's the link for today's Order";
        var supportUriBuilder = new UriBuilder("mailto:<EMAIL>?subject=Issue in Report&body=Hi FieldAssist Team,");
        var supportURL = supportUriBuilder.Uri.AbsoluteUri;
        var outputUriBuilder = new UriBuilder(outputLink);
        var actionHtml = AnchorUrlTemplate("Download Report", outputUriBuilder.Uri.AbsoluteUri);
        var urlHtml = UrlToCopyTemplate(outputUriBuilder.Uri.AbsoluteUri);
        var template = BaseTemplate("Order Summary", msg, reasonToContactSupport, supportURL, UserName, actionHtml, urlHtml);
        return template;
    }
}
