﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Libraries.CommonEnums;

namespace Library.ApiLogger.CosmosLogs;

public interface IApiLog
{
    string Category { get; }
    long CompanyId { get; }
    string Input { get; set; }
    string Output { get; set; }
    string RequestId { get; }
    string PartitionKey { get; set; }
}

public class ApiLogRequest : IApiLog
{
    public ApiLogRequest()
    {
        RequestId = Guid.NewGuid().ToString();
    }

    public ApiType ApiType { set; get; }

    public string Description { get; set; }

    /// <summary>
    ///     Time taken to process the request
    /// </summary>
    public TimeSpan Duration => ResponseTime - RequestTime;

    public Exception Exception { get; set; }

    [JsonPropertyName("id")]
    public string Id
    {
        set => _ = RequestId;
        get => RequestId;
    }

    /// <summary>
    ///     Headers in Api Request
    /// </summary>
    public Dictionary<string, string> RequestHeaders { get; set; }

    public string RequestIpAddress { get; set; }

    /// <summary>
    ///     WebApi Path which was Used to request data
    /// </summary>
    public string RequestPath { get; set; }

    /// <summary>
    ///     UTC Time when Api Action was Requested
    /// </summary>
    public DateTime RequestTime { get; set; }

    /// <summary>
    ///     UTC Timestamp when Api Action was Requested
    /// </summary>
    public long RequestTimestamp { get; set; }

    /// <summary>
    ///     Headers in Api Response
    /// </summary>
    public IDictionary<string, string> ResponseHeaders { get; set; }

    /// <summary>
    ///     UTC Time when Response was delivered
    /// </summary>
    public DateTime ResponseTime { get; set; }

    /// <summary>
    ///     Status Text/ Error Text
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    ///     Http Response Code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    ///     UserName Of the Requester
    /// </summary>
    public string UserName { get; set; }

    public string Category => ApiType.ToString();

    public long CompanyId { get; set; }
    public string PartitionKey { get; set; }

    /// <summary>
    ///     Body of the Request
    /// </summary>
    public string Input { get; set; }

    /// <summary>
    ///     Body of the Response
    /// </summary>
    public string Output { get; set; }

    /// <summary>
    ///     Guid for the Request
    /// </summary>
    public string RequestId { get; set; }
}
