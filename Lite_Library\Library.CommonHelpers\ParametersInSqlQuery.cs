﻿using System.Text.RegularExpressions;

namespace Library.CommonHelpers;

public static class ParameterizedSqlQuery
{
    public static MatchCollection GetParametersInSqlQuery(string sqlQuery, string pattern)
    {
        return Regex.Matches(sqlQuery, pattern);
    }

    public static string GetReplaceSqlQueryForPattern(string originalSqlQuery, string patternForSql)
    {
        return Regex.Replace(originalSqlQuery, patternForSql, match =>
        {
            // match.Groups[1].Value contains the parameter name without brackets
            var parameterName = "@" + match.Groups[1].Value;
            return parameterName;
        });
    }
}
