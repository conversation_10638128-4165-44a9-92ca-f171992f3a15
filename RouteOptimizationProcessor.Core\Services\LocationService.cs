﻿using Libraries.CommonEnums;
using Libraries.CommonModels;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Repositories;

namespace RouteOptimizationProcessor.Core.Services;


public interface ILocationService
{
    Task<List<Location>> GetLocationsForEmployeesAsync(long companyId, long employeeId);
}

public class LocationService(ICompanySettingsRepository companySettingsRepository,
    IEmployeeRepository employeeRepository, 
    ILocationRepository locationRepository) : ILocationService
{
    public async Task<List<Location>> GetLocationsForEmployeesAsync(long companyId, long employeeId)
    {
        var journeyVersion = await companySettingsRepository.GetJourneyPlanVersionForCompany(companyId);
        if (journeyVersion == JourneyPlanVersion.NewJourneyPlan)
        {
            return await GetLocationsAcctoNewPlan(companyId, employeeId);
        }

        return await GetLocationsAcctoOldPlan(companyId, employeeId);
    }

    private async Task<List<Location>> GetLocationsAcctoOldPlan(long companyId, long employeeId)
    {
        var journeyEntity = await companySettingsRepository.GetJourneyPlanTypeForCompany(companyId);

        switch (journeyEntity)
        {
            case JourneyPlanType.RoutePlan:
                var routes = (await employeeRepository.GetRoutesForEmployeeAsync(companyId,
                    employeeId)).Select(r => r.Id).Distinct().ToList();
                return await locationRepository.GetOutletsFromRoutesAsync(routes, companyId);
            case JourneyPlanType.Default:
            case JourneyPlanType.TourPlan:
            case JourneyPlanType.BeatPlan:
            default:
                var usesPositionCodes = await companySettingsRepository.UsesPositionCodes(companyId);
                var beatReturnList = new List<EntityMinWithErp>();

                if (usesPositionCodes)
                {
                    var positionData = await employeeRepository.GetPositionCodesOfAndUnderUserAsync(companyId, employeeId);
                    var IsManager = positionData.Item3.Any(l => l == PositionCodeLevel.L2Position || l == PositionCodeLevel.L3Position);
                    if (IsManager)
                    {
                        beatReturnList.AddRange(await employeeRepository.GetBeatsForPositionsAsync(companyId, positionData.Item2));
                    }
                    else
                    {
                        beatReturnList.AddRange(await employeeRepository.GetBeatsForPositionsAsync(companyId, positionData.Item1));
                    }
                }
                else
                {
                    beatReturnList = (await employeeRepository.GetBeatsForEmployeesAsync(employeeId, companyId)).ToList();
                }

                var locations = await locationRepository.GetOutletsFromBeatsAsync(companyId, beatReturnList.Select(b => b.Id).ToList());
                return locations;
        }
    }

    private async Task<List<Location>> GetLocationsAcctoNewPlan(long companyId, long employeeId)
    {
        var journeyEntity = await companySettingsRepository.GetNewJourneyPlanEntityForCompany(companyId);
        var usesPositionCodes = await companySettingsRepository.UsesPositionCodes(companyId);
        if (journeyEntity == JourneyPlanningEntity.Beat)
        {
            var beatReturnList = new List<EntityMinWithErp>();

            if (usesPositionCodes)
            {
                var positionData = await employeeRepository.GetPositionCodesOfAndUnderUserAsync(companyId, employeeId);
                var IsManager = positionData.Item3.Any(l => l == PositionCodeLevel.L2Position || l == PositionCodeLevel.L3Position);
                if (IsManager)
                {
                    beatReturnList.AddRange(await employeeRepository.GetBeatsForPositionsAsync(companyId, positionData.Item2));
                }
                else
                {
                    beatReturnList.AddRange(await employeeRepository.GetBeatsForPositionsAsync(companyId, positionData.Item1));
                }
            }
            else
            {
                beatReturnList = (await employeeRepository.GetBeatsForEmployeesAsync(employeeId, companyId)).ToList();
            }

            var locations = await locationRepository.GetOutletsFromBeatsAsync(companyId, beatReturnList.Select(b => b.Id).ToList());
            return locations;
        }
        else
        {
            var routes = new List<long>();
            if (usesPositionCodes)
            {
                var positionData = await employeeRepository.GetPositionCodesOfAndUnderUserAsync(companyId, employeeId);
                var IsManager = positionData.Item3.Any(l => l == PositionCodeLevel.L2Position || l == PositionCodeLevel.L3Position);
                if (IsManager)
                {
                    routes.AddRange((await employeeRepository.GetRoutesForPositions(companyId, positionData.Item2)).Select(r => r.Id).Distinct().ToList());
                }
                else
                {
                    routes.AddRange((await employeeRepository.GetRoutesForPositions(companyId, positionData.Item1)).Select(r => r.Id).Distinct().ToList());
                }
            }
            else
            {
                routes = (await employeeRepository.GetRoutesForEmployeeViaDirectMappingAsync(companyId, employeeId))
                    .Select(r => r.Id).Distinct().ToList();
            }


            return await locationRepository.GetOutletsFromRoutesAsync(routes, companyId);
        }
    }

}
