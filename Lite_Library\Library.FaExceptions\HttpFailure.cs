﻿using System;
using System.Net;
using Library.CommonHelpers;

namespace Library.FaExceptions;

public class HttpFailure : Exception
{
    public readonly string baseUrl;
    public readonly string message;
    public readonly string path;
    public readonly HttpStatusCode status;
    public readonly string statusMessage;

    public HttpFailure(string baseUrl, string path, HttpStatusCode status, string message) : base(message)
    {
        this.baseUrl = baseUrl;
        this.path = path;
        this.status = status;
        this.message = message;
        statusMessage = status.GetDisplayName();
    }

    public HttpFailure(string baseUrl, string path, string statusMessage, string message) : base(message)
    {
        this.baseUrl = baseUrl;
        this.path = path;
        this.statusMessage = statusMessage;
        this.message = message;
        _ = Enum.TryParse(statusMessage, out status);
    }

    public override string ToString()
    {
        return $"API {baseUrl + path} failed with status: {statusMessage} and message: {message}" + base.ToString();
    }
}
