﻿using Libraries.CommonEnums;

namespace RouteOptimizationProcessor.Core.Repositories;

public interface ICompanySettingsRepository
{
    Task<Dictionary<string, object>> GetAllSettings(long companyId);
    Task<JourneyPlanVersion> GetJourneyPlanVersionForCompany(long companyId);
    Task<JourneyPlanType> GetJourneyPlanTypeForCompany(long companyId);
    Task<JourneyPlanningEntity> GetNewJourneyPlanEntityForCompany(long companyId);
    Task<bool> UsesPositionCodes(long companyId);
    Task<TimeSpan> GetTimeZoneOffset(long companyId);
}