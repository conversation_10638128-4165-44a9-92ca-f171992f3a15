﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="prometheus-net" Version="8.2.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.4" />
    <PackageReference Include="Opw.HttpExceptions" Version="4.0.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
  </ItemGroup>
</Project>
