﻿using Libraries.CommonEnums;
using Libraries.CommonModels;
using RouteOptimizationProcessor.Core.Repositories;

namespace RouteOptimizationProcessor.Core.Services;

public interface ICohortService
{
    Task<List<long>> GetEmployeesFromCohortAsync(long companyId, long cohortId);
}

public class CohortService(ICohortRepository cohortRepository, 
    IEmployeeRepository employeeRepository, 
    IPositionCodeEntityMappingRepository positionCodeEntityMappingRepository) : ICohortService
{
    public async Task<List<long>> GetEmployeesFromCohortAsync(long companyId, long cohortId)
    {
        var dbCohort = await cohortRepository.GetCohortSingleAsync(cohortId, companyId);
        if (dbCohort == null)
        {
            return [];
        }

        List<long> userIds = [];


        switch (dbCohort.UserPlatform)
        {
            case UserPlatform.UserApp or UserPlatform.AnalyticApp
                when dbCohort.ExtraInfoJsonModel is { UserInfos: not null, EmployeeRanks: not null }:
                {
                    foreach (var userInfo in dbCohort.ExtraInfoJsonModel.UserInfos)
                    {
                        PortalUserRole userRole;
                        switch (userInfo.Rank)
                        {
                            default:
                                userRole = PortalUserRole.GlobalSalesManager;
                                break;

                            case EmployeeRank.ESM:
                                userRole = PortalUserRole.ClientEmployee;
                                break;

                            case EmployeeRank.ASM:
                                userRole = PortalUserRole.AreaSalesManager;
                                break;

                            case EmployeeRank.RSM:
                                userRole = PortalUserRole.RegionalSalesManager;
                                break;

                            case EmployeeRank.ZSM:
                                userRole = PortalUserRole.ZonalSalesManager;
                                break;

                            case EmployeeRank.NSM:
                                userRole = PortalUserRole.NationalSalesManager;
                                break;
                        }

                        EmployeeType? employeeType = null;
                        var employeeTypes = dbCohort.ExtraInfoJsonModel.EmployeeTypes;
                        if (employeeTypes != null)
                        {
                            if (employeeTypes.Count == 1 && employeeTypes.First() == EmployeeType.SR)
                            {
                                employeeType = EmployeeType.SR;
                            }
                            else if (employeeTypes.Count == 1 && employeeTypes.First() == EmployeeType.DSR)
                            {
                                employeeType = EmployeeType.DSR;
                            }
                        }

                        var users = await employeeRepository.GetFieldUserIdsUnderManagerModelAsync(companyId,
                            userRole, userInfo.Id, employeeType);

                        var employeeRanks = dbCohort.ExtraInfoJsonModel.EmployeeRanks;

                        var ids = users.Where(s => employeeRanks.Contains(s.UserRank)).Select(s => s.Id).ToList();

                        userIds.AddRange(ids);
                    }

                    break;
                }
            case UserPlatform.UserApp or UserPlatform.AnalyticApp
                when dbCohort.ExtraInfoJsonModel is { PositionInfos: not null, PositionCodeLevels: not null }:
                {
                    foreach (var positionInfo in dbCohort.ExtraInfoJsonModel.PositionInfos)
                    {
                        var positionCodeLevelsList = dbCohort.ExtraInfoJsonModel.PositionCodeLevels;
                        var employeeType = dbCohort.ExtraInfoJsonModel.EmployeeTypes;

                        // Get all the positions under position codes in cohort
                        var hierarchy = await positionCodeEntityMappingRepository
                            .GetAllPositionCodesUnderUserAsync(companyId, positionInfo.PositionId, false, true);

                        var filteredEntityIds = hierarchy.Where(p => positionCodeLevelsList.Contains(p.PositionCodeLevel)).Select(p => p.EntityId).Distinct().ToList();


                        if (positionCodeLevelsList.Contains(positionInfo.Level))
                        {
                            var parentPositionId = await positionCodeEntityMappingRepository.
                                GetPositionUsersAsync(companyId, positionInfo.PositionId);
                            filteredEntityIds.AddRange(parentPositionId.Select(item => item.EntityId));
                        }

                        List<EmployeeMinWithType> employees;
                        if (dbCohort.UserPlatform == UserPlatform.AnalyticApp)
                        {
                            employees = await employeeRepository.GetAllEmployeesAsync(companyId);
                            var ids = employees.Where(e => filteredEntityIds.Contains(e.Id)).Select(e => e.Id).ToList();
                            userIds.AddRange(ids);
                        }
                        else
                        {
                            employees = await employeeRepository.GetAllEmployeesSRAndDSRAsync(companyId);
                            var ids = employees.Where(e => filteredEntityIds.Contains(e.Id) && employeeType != null 
                                    && employeeType.Contains(e.UserType)).Select(e => e.Id).ToList();
                            userIds.AddRange(ids);
                        }
                    }

                    break;
                }
        }

        return userIds;
    }
}
