﻿using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.DbModels;

namespace RouteOptimizationProcessor.DbStorage.DbContexts;

public class MasterDbContext(DbContextOptions<MasterDbContext> options) : DbContext(options)
{
    public DbSet<CompanySetting> CompanySettings { get; set; }
    public DbSet<CompanySettingValue> CompanySettingValues { get; set; }
    public DbSet<CountryDetail> CountryDetails { get; set; }
    public DbSet<RouteAutomationConfiguration> RouteAutomationConfigurations { get; set; }
    public DbSet<Cohort> Cohorts { get; set; }
    public DbSet<LocationBeat> LocationBeats { get; set; }
    public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
    public DbSet<PositionBeatMapping> PositionBeatMapping { get; set; }
    public DbSet<PositionCode> PositionCodes { get; set; }
    public DbSet<ClientEmployee> ClientEmployees { get; set; }
    public DbSet<FAEmployeeBeatMappings> FAEmployeeBeatMappings { get; set; }
    public DbSet<Route> Routes { get; set; }
    public DbSet<RouteOutletMapping> RouteOutletMappings { get; set; }
    public DbSet<RoutePlan> RoutePlans { get; set; }
    public DbSet<RoutePlanItem> RoutePlanItems { get; set; }
    public DbSet<EmployeeRouteMapping> FAEmployeeRouteMappings { get; set; }
    public DbSet<RoutePositionMappings> RoutePositionMappings { get; set; }
    public DbSet<Location> Locations { get; set; }
    public DbSet<OutletSegmentationAttributes> OutletSegmentationAttributes { get; set; }
    public DbSet<TheShopType> ShopTypes { get; set; }

    public DbSet<Channel> Channels { get; set; }
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) { }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {

    }

    public override int SaveChanges()
    {
        throw new InvalidOperationException("The context is readonly");
    }

    public override Task<int> SaveChangesAsync(
        CancellationToken cancellationToken = default(CancellationToken)
    )
    {
        throw new InvalidOperationException("The context is readonly");
    }
}
