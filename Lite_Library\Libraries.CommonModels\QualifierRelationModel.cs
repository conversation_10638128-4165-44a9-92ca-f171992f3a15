﻿// Licensed to the.NET Foundation under one or more agreements.
// The.NET Foundation licenses this file to you under the MIT license.

using System.Collections.Generic;

namespace Libraries.CommonModels;
public class QualifierGroup
{
    public List<long>? QualifierIds { get; set; }
    public string Operation { get; set; } = "AND"; // "AND" or "OR"
}

public class QualifierRelationModel
{
    public string Operation { get; set; } = "AND"; // Root operation: "AND" or "OR"
    public List<QualifierGroup>? QualifiersRelations { get; set; }
}
