﻿using System.Collections.Generic;
using Libraries.CommonEnums;
using Library.SlackService.Model;

namespace Library.SlackService;

public enum BugChannel
{
    Unknown = 0,
    Debug = 1,
    Manage = 2,
    BugWatch = 3,
    ExternalApiErrors = 4,
    Api400Errors = 5,
    TimeOuts = 6
}

public class SlackExceptionMessage
{
    private static readonly Dictionary<ApiType, string> _botDictionary = new() { [ApiType.FAApi] = "MobileApi", [ApiType.ExtApi] = "ExternalApi", [ApiType.ManagerApi] = "ManagerApp", [ApiType.Unknown] = "" };

    private static readonly Dictionary<BugChannel, string> _channelDictionary = new()
    {
        [BugChannel.Debug] = "#debugdetectedbugs",
        [BugChannel.Manage] = "#managedetectedbugs",
        [BugChannel.BugWatch] = "#bugwatch",
        [BugChannel.ExternalApiErrors] = "#extapimanageapibugs",
        [BugChannel.Api400Errors] = "#mobile400apibugs",
        [BugChannel.TimeOuts] = "#timeoutapibugs",
        [BugChannel.Unknown] = ""
    };

    private readonly ApiType _apiType;
    private readonly string _baseLink;
    private readonly BugChannel _bugChannel;

    public SlackExceptionMessage(BugChannel bugChannel, ApiType botType = 0)
    {
        _apiType = botType;
        _bugChannel = bugChannel;
        _baseLink = bugChannel == BugChannel.Debug
            ? "https://debug.fieldassist.in"
            : "https://manage.fieldassist.in";
    }

    public string ErrorMessage { get; set; }
    public int HashedToken { get; set; }
    public string Heading { get; set; }
    public string RequestId { get; set; }

    public ISlackMessage GetSlackMessage()
    {
        var val = new SlackMessage
        {
            Text = Heading, Username = _botDictionary[_apiType], Channel = _channelDictionary[_bugChannel], Attachments = new List<Attachment> { new() { FallBack = ErrorMessage, Title = ErrorMessage, Fields = new List<Field>() } }
        };

        if (val.Attachments.Count > 0)
        {
            if (!string.IsNullOrWhiteSpace(RequestId))
            {
                val.Attachments[0].Fields.Add(new Field
                {
                    Title = "Link To Request",
                    Value =
                        $"{_baseLink}/FieldAssistPoc/ApiLogs/GetCompleteRequest?reqId={RequestId}&apiType={_apiType}"
                });
                val.Attachments[0].Fields.Add(new Field { Title = "RequestId", Value = RequestId, Short = true });
            }

            if (HashedToken != 0)
            {
                val.Attachments[0].Fields.Add(new Field { Title = "Token Ref", Value = HashedToken.ToString(), Short = true });
            }
        }

        return val;
    }
}
