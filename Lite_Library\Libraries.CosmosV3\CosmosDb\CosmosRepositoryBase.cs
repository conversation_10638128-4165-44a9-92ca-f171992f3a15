﻿using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json.Linq;

namespace Libraries.CosmosV3.CosmosDb;

public abstract class CosmosRepositoryBase
{
    private readonly Container container;

    protected CosmosRepositoryBase(CosmosDbBase db,
        string containerName)
    {
        container = db.GetContainer(containerName);
    }

    protected async Task AddItemProtectedAsync<T>(T item)
    {
        await container.CreateItemAsync(item).ConfigureAwait(false);
    }

    protected async Task AddOrUpdateItemProtectedAsync<T>(T item, PartitionKey? partitionKey = null)
    {
        await container.UpsertItemAsync(item, partitionKey).ConfigureAwait(false);
    }

    protected async Task DeleteItemProtectedAsync(string id, PartitionKey partitionKey)
    {
        await container.DeleteItemAsync<JObject>(id, partitionKey).ConfigureAwait(false);
    }

    protected virtual FeedIterator<T> GetFeedIterator<T>(string queryString, PartitionKey partitionKey)
    {
        var feedIterator = container.GetItemQueryIterator<T>(new QueryDefinition(queryString), null, new QueryRequestOptions { EnableScanInQuery = false, PartitionKey = partitionKey });
        return feedIterator;
    }

    protected async Task UpdateItemProtectedAsync<T>(T item, PartitionKey partitionKey)
    {
        await container.UpsertItemAsync(item, partitionKey).ConfigureAwait(false);
    }

    public async Task<IEnumerable<T>> GetCrossPartitionItemsAsync<T>(string queryString)
    {
        var query = container.GetItemQueryIterator<T>(new QueryDefinition(queryString), null, new QueryRequestOptions { EnableScanInQuery = true });
        var results = new List<T>();
        while (query.HasMoreResults)
        {
            var response = await query.ReadNextAsync().ConfigureAwait(false);
            results.AddRange(response.ToList());
        }

        return results;
    }

    public virtual async Task<T> GetItemAsync<T>(string id, PartitionKey partitionKey)
    {
        try
        {
            var response = await container.ReadItemAsync<T>(id, partitionKey).ConfigureAwait(false);
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return default;
        }
    }

    public async Task<T> GetItemAsync<T>(string id, string partitionKey)
    {
        return await GetItemAsync<T>(id, new PartitionKey(partitionKey)).ConfigureAwait(false);
    }

    public async Task<IEnumerable<T>> GetItemsAsync<T>(string partitionKey)
    {
        return await GetItemsAsync<T>("select * from c", new PartitionKey(partitionKey)).ConfigureAwait(false);
    }

    public async Task<IEnumerable<T>> GetItemsAsync<T>(string query, string partitionKey)
    {
        return await GetItemsAsync<T>(query, new PartitionKey(partitionKey)).ConfigureAwait(false);
    }

    public virtual async Task<IEnumerable<T>> GetItemsAsync<T>(string queryString, PartitionKey partitionKey)
    {
        var query = container.GetItemQueryIterator<T>(new QueryDefinition(queryString), null, new QueryRequestOptions { EnableScanInQuery = false, PartitionKey = partitionKey });
        var results = new List<T>();
        while (query.HasMoreResults)
        {
            var response = await query.ReadNextAsync().ConfigureAwait(false);
            results.AddRange(response.ToList());
        }

        return results;
    }

    public async Task<JObject> GetLatestRecord(string partitionKey)
    {
        var query = "select top 1 * from c order by c._ts desc";
        var response = await GetFeedIterator<JObject>(query, new PartitionKey(partitionKey))
            .ReadNextAsync().ConfigureAwait(false);
        var item = response.FirstOrDefault();
        return item;
    }

    public async Task<IEnumerable<T>> GetMultiPartitionItemsAsync<T>(string queryString, List<string> partitions, string partitionKeyPlaceholder = "$partitionKey")
    {
        var results = new List<T>();
        var tasks = new List<Task<IEnumerable<T>>>();
        var counter = 0;
        foreach (var partitionKey in partitions)
        {
            var querySQL = queryString.Replace(partitionKeyPlaceholder, partitionKey);
            tasks.Add(GetItemsAsync<T>(querySQL, new PartitionKey(partitionKey)));
            if (++counter % 10 == 0)
            {
                await Task.WhenAll(tasks).ConfigureAwait(false);
            }
        }

        var res = await Task.WhenAll(tasks).ConfigureAwait(false);
        results.AddRange(res.SelectMany(x => x));
        return results;
    }

    public async Task<object> GetObjectForQueryAsync(string query, string partitionKey)
    {
        return (await GetItemsAsync<object>(query, new PartitionKey(partitionKey)).ConfigureAwait(false)).FirstOrDefault();
    }
}
