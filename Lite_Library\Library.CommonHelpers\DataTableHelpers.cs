﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Library.CommonHelpers;

internal class DataColumnComparer : IEqualityComparer<DataColumn>
{
    public bool Equals(DataColumn x, DataColumn y)
    {
        return x.Caption == y.Caption;
    }

    public int GetHashCode(DataColumn obj)
    {
        return obj.Caption.GetHashCode();
    }
}

/// <summary>
///  Contains Helper Methods like Joins between Multiple Data Tables
/// </summary>
public static class DataTableHelpers
{
    /// <summary>
    /// Full Outer Join Euivalent of SQL for Data Tables.
    /// Note: It assume it the DataTables have a Primary Key with Name "Id" other columns can be common but the Value from DataTable 1 will be prefered.
    /// </summary>
    /// <param name="dataTable1"></param>
    /// <param name="dataTable2"></param>
    /// <returns>Full Join result of the two Data Tables</returns>
    public static DataTable fulljoin(DataTable dataTable1, DataTable dataTable2)
    {
        const string joinColumn = "Id";
        var result = dataTable1.Copy();
        var columnsExclusiveToTable2 = new List<string>();
        var columnsInTable2 = dataTable2.Columns.Cast<DataColumn>().Select(column => column.ColumnName).ToList();

        foreach (var column in dataTable2.Columns.Cast<DataColumn>())
        {
            if (!result.Columns.Contains(column.ColumnName))
            {
                result.Columns.Add(column.ColumnName, column.DataType);
                columnsExclusiveToTable2.Add(column.ColumnName);
            }
        }

        var resultTableDictionary = result.AsEnumerable().ToDictionary(row => row[joinColumn]);

        foreach (var row in dataTable2.AsEnumerable())
        {
            if (resultTableDictionary.TryGetValue(row[joinColumn], out var matchedRow))
            {
                foreach (var column in columnsExclusiveToTable2)
                {
                    matchedRow[column] = row[column];
                }
            }
            else
            {
                var newRow = result.NewRow();
                newRow[joinColumn] = row[joinColumn];

                foreach (var column in columnsInTable2)
                {
                    newRow[column] = row[column];
                }

                result.Rows.Add(newRow);
            }
        }

        return result;
    }

    #region oldcode

    /*
        const string joinColumn = "Id";
        var resultTableFromTable1 = dataTable1.Clone();
        var table2Copy = dataTable2.Copy();

        var dataTable1Columns = dataTable1.Columns.Cast<DataColumn>().Select(dc =>
            new DataColumn(dc.ColumnName, dc.DataType, dc.Expression, dc.ColumnMapping)).ToArray();
        var dataTable2Columns = dataTable2.Columns.Cast<DataColumn>().Select(dc =>
            new DataColumn(dc.ColumnName, dc.DataType, dc.Expression, dc.ColumnMapping)).ToArray();

        foreach (var column in dataTable2Columns.Where(dc => !resultTableFromTable1.Columns.Contains(dc.ColumnName)))
        {
            resultTableFromTable1.Columns.Add(column);
        }

        var dataTable2ColumnNames = dataTable2Columns.Select(dc => dc.ColumnName).ToArray();
        foreach (var x in dataTable1Columns.Where(x => x.ColumnName != joinColumn && dataTable2ColumnNames.Contains(x.ColumnName)))
        {
            table2Copy.Columns.Remove(x.ColumnName);
        }

        var dictB = table2Copy.AsEnumerable().ToDictionary(row => row[joinColumn]);
        var tempDictB = dataTable2.AsEnumerable().ToDictionary(row => row[joinColumn]);
        foreach (DataRow row1 in dataTable1.Rows)
        {
            var itemArray1 = row1.ItemArray;
            if (dictB.TryGetValue(row1[joinColumn], out var row2))
            {
                var itemArray2 = row2.ItemArray;

                var combinedArray = itemArray1.Concat(itemArray2);
                resultTableFromTable1.Rows.Add(combinedArray);
                tempDictB.Remove(itemArray1[0]);
            }
            else
            {
                resultTableFromTable1.Rows.Add(itemArray1.Concat(new object[table2Copy.Columns.Count - 1]).ToArray());
            }
        }

        foreach (var remainingRow in tempDictB.Values)
        {
            var newRow = resultTableFromTable1.NewRow();
            foreach (var col in dataTable2ColumnNames)
            {
                newRow[col] = remainingRow[col];
            }

            resultTableFromTable1.Rows.Add(newRow);
        }

        return resultTableFromTable1;*/

    #endregion

    public static DataTable leftjoin(DataTable dtblA, DataTable dtblB, string joincolumn)
    {
        var targetTable = dtblA.Clone();
        var dt2Columns = dtblB.Columns.OfType<DataColumn>().Select(dc =>
            new DataColumn(dc.ColumnName, dc.DataType, dc.Expression, dc.ColumnMapping));
        var dt2FinalColumns = from dc in dt2Columns.AsEnumerable()
            where targetTable.Columns.Contains(dc.ColumnName) == false
            select dc;

        targetTable.Columns.AddRange(dt2FinalColumns.ToArray());
        var rowData = from row1 in dtblA.AsEnumerable()
            join row2 in dtblB.AsEnumerable()
                on row1[joincolumn] equals row2[joincolumn]
            select row1.ItemArray.Concat(row2.ItemArray.Where(r2 => row1.ItemArray.Contains(r2) == false))
                .ToArray();
        if (joincolumn == null)
        {
            rowData = from row1 in dtblA.AsEnumerable()
                from row2 in dtblB.AsEnumerable()
                select row1.ItemArray.Concat(row2.ItemArray).ToArray();
        }

        foreach (var values in rowData)
        {
            targetTable.Rows.Add(values);
        }

        return targetTable;
    }

    public static DataTable FullOuterJoinDataTables(params DataTable[] datatables)
    {
        var result = datatables.First().Clone();

        var commonColumns = result.Columns.OfType<DataColumn>();

        foreach (var dt in datatables.Skip(1))
        {
            commonColumns = commonColumns.Intersect(dt.Columns.OfType<DataColumn>(), new DataColumnComparer());
        }

        result.PrimaryKey = commonColumns.ToArray();
        foreach (var dt in datatables)
        {
            result.Merge(dt, false, MissingSchemaAction.AddWithKey);
        }

        return result;
    }

    /// <summary>
    /// Full Outer Join Euivalent of SQL for Data Tables
    /// </summary>
    /// <param name="datatables"></param>
    /// <returns></returns>
    public static DataTable FullOuterJoinDataTables(IEnumerable<DataTable> datatables)
    {
        var result = datatables.First().Clone();

        var commonColumns = result.Columns.OfType<DataColumn>();

        foreach (var dt in datatables.Skip(1))
        {
            commonColumns = commonColumns.Intersect(dt.Columns.OfType<DataColumn>(), new DataColumnComparer());
        }

        result.PrimaryKey = commonColumns.ToArray();

        foreach (var dt in datatables)
        {
            result.Merge(dt, false, MissingSchemaAction.AddWithKey);
        }

        return result;
    }

    /// <summary>
    /// Left Join Equivalent of SQL for Data Tables. First Data Table is assumed to be the Left Data Table
    /// </summary>
    /// <param name="dataTables"></param>
    /// <returns></returns>
    public static DataTable LeftJoinDataTables(IEnumerable<DataTable> dataTables)
    {
        var resultTable = dataTables.First().Copy();

        foreach (var dt in dataTables.Skip(1))
        {
            var commonColumns = resultTable.Columns.OfType<DataColumn>();
            commonColumns = commonColumns.Intersect(dt.Columns.OfType<DataColumn>(), new DataColumnComparer());
            var commonColumnsString = commonColumns.Select(d => d.ColumnName).ToList();
            resultTable = LeftJoin(resultTable, dt, commonColumnsString);
        }

        return resultTable;
    }

    /// <summary>
    /// Left Join Equivalent of SQL for Data Tables. First Data Table is assumed to be the Left Data Table
    /// </summary>
    /// <param name="dataTables"></param>
    /// <returns></returns>
    public static DataTable LeftJoinDataTables(params DataTable[] dataTables)
    {
        var resultTable = dataTables.First().Copy();

        foreach (var dt in dataTables.Skip(1))
        {
            var commonColumns = resultTable.Columns.OfType<DataColumn>();
            commonColumns = commonColumns.Intersect(dt.Columns.OfType<DataColumn>(), new DataColumnComparer());
            var commonColumnsString = commonColumns.Select(d => d.ColumnName).ToList();
            resultTable = LeftJoin(resultTable, dt, commonColumnsString);
        }

        return resultTable;
    }

    public static bool IsNumeric(this DataColumn column)
    {
        var type = column.DataType;
        return DataTypeHelper.IsNumeric(type);
    }

    /// <summary>
    /// Converts a data table to list
    /// This method uses reflection - can have performance impact
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="dataTable"></param>
    /// <returns></returns>
    public static List<T> ConvertToList<T>(DataTable dataTable)
    {
        var objects = new List<T>();

        foreach (DataRow row in dataTable.Rows)
        {
            // Create an instance of T using reflection
            var obj = Activator.CreateInstance<T>();

            foreach (DataColumn col in dataTable.Columns)
            {
                var prop = obj.GetType().GetProperty(col.ColumnName, BindingFlags.Public | BindingFlags.Instance);
                if (prop != null && row[col] != DBNull.Value)
                {
                    var propType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;

                    //var data = row[col];

                    if (propType.IsEnum)
                    {
                        var isInteger = int.TryParse(row[col]?.ToString(), out var enumIntValue);

                        if (isInteger)
                        {
                            prop.SetValue(obj, Enum.ToObject(propType, row[col]));
                        }
                        else
                        {
                            var value = (int)Enum.Parse(propType, row[col]?.ToString());
                            prop.SetValue(obj, Enum.ToObject(propType, value));
                        }
                    }
                    else
                    {
                        // use this if conversion fails for nullable types :
                        // https://stackoverflow.com/questions/3531318/convert-changetype-fails-on-nullable-types
                        prop.SetValue(obj, Convert.ChangeType(row[col], propType), null);
                    }
                }
            }

            objects.Add(obj);
        }

        return objects;
    }

    /// <summary>
    /// Loads Values into DataTable from a DbDataReader instance. Tested for use in ClickHouseSqlDataReader.
    /// </summary>
    /// <param name="dataTable"></param>
    /// <param name="dbDataReader"></param>
    /// <returns></returns>
    public static async Task LoadManuallyFromDbDataReader(this DataTable dataTable, DbDataReader dbDataReader)
    {
        if (dbDataReader.HasRows)
        {
            // Apparently dt.Load(reader) did not work with
            // Add columns to DataTable based on schema information
            for (var i = 0; i < dbDataReader.FieldCount; i++)
            {
                var columnName = dbDataReader.GetName(i);
                var dataType = dbDataReader.GetFieldType(i);

                // Add column to DataTable with the same name and data type
                dataTable.Columns.Add(columnName, dataType);
            }

            // Read data from reader and populate DataTable
            while (await dbDataReader.ReadAsync().ConfigureAwait(false))
            {
                var row = dataTable.NewRow();

                for (var i = 0; i < dbDataReader.FieldCount; i++)
                {
                    // Populate each column in the DataRow
                    row[i] = dbDataReader.GetValue(i);
                }

                // Add populated DataRow to DataTable
                dataTable.Rows.Add(row);
            }
        }
    }

    /// <summary>
        /// Convert an IEnumerable<T> to a DataTable where T is a class
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="items"></param>
        /// <returns></returns>
        public static DataTable GetDataTable<T>(IEnumerable<T> items)
        {
            DataTable dataTable = new(typeof(T).Name);

            //Get all the properties
            var Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(prop => prop.Name != "Id")
                          .ToArray();

            foreach (var prop in Props)
            {
                //Setting column names as Property names
                dataTable.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            }

            foreach (var item in items)
            {
                var values = new object[Props.Length];
                for (var i = 0; i < Props.Length; i++)
                {
                    //if (Props[i].Name != "Id")
                    values[i] = Props[i].GetValue(item, null);
                }

                dataTable.Rows.Add(values);
            }
            //put a breakpoint here and check datatable
            return dataTable;
        }

        #region Private

    private static DataTable LeftJoin(DataTable leftDataTable, DataTable rightDataTable, IEnumerable<string> commonColumnsString)
    {
        var query = from row1 in leftDataTable.Rows.Cast<DataRow>()
            join row2 in rightDataTable.Rows.Cast<DataRow>()
                on string.Join("$", GetJoinKey(row1, commonColumnsString).Select(d => d.ToString())) equals string.Join("$", GetJoinKey(row2, commonColumnsString).Select(d => d.ToString())) into joinedRows
            from row2 in joinedRows.DefaultIfEmpty()
            select new { row1, row2 };
        var resultTable = CreateResultTable(commonColumnsString, leftDataTable, rightDataTable);
        foreach (var item in query)
        {
            var values = GetValues(item.row1, commonColumnsString);
            values.AddRange(GetNonCommonValues(item.row1, commonColumnsString));
            values.AddRange(GetNonCommonValues(item.row2, commonColumnsString));
            resultTable.Rows.Add(values.ToArray());
        }

        return resultTable;
    }

    private static List<object> GetNonCommonValues(DataRow row, IEnumerable<string> commonColumnsString)
    {
        var nonCommonValues = new List<object>();
        if (row == null)
        {
            return nonCommonValues;
        }

        foreach (DataColumn column in row.Table.Columns)
        {
            if (!commonColumnsString.Contains(column.ColumnName))
            {
                nonCommonValues.Add(row[column]);
            }
        }

        return nonCommonValues;
    }

    private static DataTable CreateResultTable(IEnumerable<string> commonColumns, DataTable table1, DataTable table2)
    {
        var resultTable = new DataTable();
        foreach (var column in commonColumns)
        {
            resultTable.Columns.Add(column, table1.Columns[column].DataType);
        }

        foreach (DataColumn column in table1.Columns)
        {
            if (!commonColumns.Contains(column.ColumnName))
            {
                resultTable.Columns.Add(column.ColumnName, column.DataType);
            }
        }

        foreach (DataColumn column in table2.Columns)
        {
            if (!commonColumns.Contains(column.ColumnName) && !resultTable.Columns.Contains(column.ColumnName))
            {
                resultTable.Columns.Add(column.ColumnName, column.DataType);
            }
        }

        return resultTable;
    }

    private static List<object> GetValues(DataRow row, IEnumerable<string> columns)
    {
        var values = new List<object>();
        foreach (var column in columns)
        {
            values.Add(row[column]);
        }

        return values;
    }

    private static object[] GetJoinKey(DataRow row, IEnumerable<string> columns)
    {
        return GetValues(row, columns).ToArray();
    }

    #endregion
}
