﻿namespace Libraries.CommonEnums;

public enum CategoryEnum
{
    PrimaryCategory = 1,
    SecondaryCategory = 2,
    ProductDivision = 3
}

public enum ProductHierarchy
{
    NoFilter = 0,
    Division = 10,
    PrimaryCategory = 20,
    SecondaryCategory = 30,
    Product = 40,
    DisplayCategory = 50
}

public enum ProductHierarchyEnum
{
    Product = 0,
    SecondaryCategory = 1,
    PrimaryCategory = 2,
    FocussedProduct = 3,
    ProductMustSell = 4,
    Assorted = 5,
    All = 6,
    ProductDivision = 7
}

public enum FactoryProductType
{
    All = 0,
    ProductDivision = 1,
    PrimaryCategory = 2,
    SecondaryCategory = 3,
    Product = 4
}

public enum SKUOrderColor
{
    Red,
    Orange,
    Blue,
    Green
}

public enum ProductHierarchyMasterMeasures
{
    Product = 0,
    PrimaryCategory = 1,
    SecondaryCategory = 2,
    ProductDivision = 3,
    FocussedProduct = 4
}
