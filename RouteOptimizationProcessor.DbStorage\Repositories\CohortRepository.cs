﻿using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class CohortRepository(MasterDbContext masterDbContext) : ICohortRepository
{
    public async Task<Cohort?> GetCohortSingleAsync(long cohort, long companyId)
    {
        return await masterDbContext.Cohorts
               .Where(c => c.Id == cohort && c.CompanyId == companyId)
               .SingleOrDefaultAsync();
    }
}
