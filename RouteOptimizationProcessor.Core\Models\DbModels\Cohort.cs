﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace RouteOptimizationProcessor.Core.Models.DbModels;

public class Cohort
{
    public long Id { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public long CompanyId { get; set; }
    public bool Deleted { get; set; }
    public UserPlatform UserPlatform { get; set; }
    public required string ExtraInfoJson { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [NotMapped]
    public ExtraInfoJson ExtraInfoJsonModel => JsonSerializer.Deserialize<ExtraInfoJson>(ExtraInfoJson);
}

[NotMapped]
public class ExtraInfoJson
{
    public List<UserInfo>? UserInfos { get; set; }
    public List<PositionInfo>? PositionInfos { get; set; }
    public List<PositionCodeLevel>? PositionCodeLevels { get; set; }
    public List<EmployeeRank>? EmployeeRanks { get; set; }
    public List<EmployeeType>? EmployeeTypes { get; set; }
    public List<long>? ProductDivisions { get; set; }
    public List<GeographyInfo>? GeographyInfos { get; set; }
    public OutletAttributes? OutletAttributes { get; set; }
}

[NotMapped]
public class PositionInfo
{
    public List<long>? PositionId { get; set; }
    public PositionCodeLevel Level { get; set; }
}

[NotMapped]
public class UserInfo
{
    public List<long>? Id { get; set; }
    public EmployeeRank Rank { get; set; }
}

[NotMapped]
public class GeographyInfo
{
    public List<long>? GeographyIds { get; set; }
    public GeographyHierarchy Level { get; set; }
}

[NotMapped]
public class OutletAttributes
{
    public List<OutletChannel>? OutletChannel { get; set; }
    public List<string>? OutletShopType { get; set; }
    public List<OutletSegmentation>? OutletSegmentation { get; set; }
    public bool IsFocused { get; set; }
}
