﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;
using System.Text.Json;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class CompanySettingsRepository(MasterDbContext masterDbContext) : ICompanySettingsRepository
{
    public async Task<Dictionary<string, object>> GetAllSettings(long companyId)
    {
        var settingsdb = await masterDbContext.CompanySettings.Where(s => !s.IsDeprecated)
            .GroupJoin(masterDbContext.CompanySettingValues.Where(v => v.CompanyId == companyId).DefaultIfEmpty(),
                s => s.Id, v => v.SettingId,
                (s, v) => new { s.SettingKey, s.SettingType, s.DefaultValue, v.FirstOrDefault().SettingValue })
            .ToListAsync();
        var settings = settingsdb.ToDictionary(k => k.<PERSON>,
            v => ConvertFrom(v.SettingType, v?.SettingValue, v.DefaultValue));
        return settings;
    }

    public async Task<JourneyPlanVersion> GetJourneyPlanVersionForCompany(long companyId)
    {
        var id = await masterDbContext.CompanySettings.Where(p => p.SettingKey == "JourneyPlanVersion").FirstOrDefaultAsync();
        var result = await masterDbContext.CompanySettingValues.Where(p => p.CompanyId == companyId && p.SettingId == id.Id).Select(s => s.SettingValue).FirstOrDefaultAsync();

        return (JourneyPlanVersion)Enum.Parse(typeof(JourneyPlanVersion), result.Replace(" ", "").Trim() ?? id.DefaultValue.Replace(" ", "").Trim());
    }

    public async Task<JourneyPlanType> GetJourneyPlanTypeForCompany(long companyId)
    {
        var id = await masterDbContext.CompanySettings.Where(p => p.SettingKey == "JourneyPlanType").FirstOrDefaultAsync();
        var result = await masterDbContext.CompanySettingValues.Where(p => p.CompanyId == companyId && p.SettingId == id.Id).Select(s => s.SettingValue).FirstOrDefaultAsync();

        return (JourneyPlanType)Enum.Parse(typeof(JourneyPlanType), result.Replace(" ", "").Trim() ?? id.DefaultValue.Replace(" ", "").Trim());
    }

    public async Task<JourneyPlanningEntity> GetNewJourneyPlanEntityForCompany(long companyId)
    {
        var id = await masterDbContext.CompanySettings.Where(p => p.SettingKey == "JourneyPlanningEntity").FirstOrDefaultAsync();
        var result = await masterDbContext.CompanySettingValues.Where(p => p.CompanyId == companyId && p.SettingId == id.Id).Select(s => s.SettingValue).FirstOrDefaultAsync();

        return (JourneyPlanningEntity)Enum.Parse(typeof(JourneyPlanningEntity), result.Replace(" ", "").Trim() ?? id.DefaultValue.Replace(" ", "").Trim());
    }

    public async Task<bool> UsesPositionCodes(long companyId)
    {
        var id = await masterDbContext.CompanySettings.Where(p => p.SettingKey == "UsesPositionCodes").FirstOrDefaultAsync();
        var result = await masterDbContext.CompanySettingValues.Where(p => p.CompanyId == companyId && p.SettingId == id.Id).Select(s => s.SettingValue).FirstOrDefaultAsync();

        return Convert.ToBoolean(result != null ? result : id.DefaultValue);
    }

    public async Task<TimeSpan> GetTimeZoneOffset(long companyId)
    {
        var timeZoneOffsetMinutes = await (from cd in masterDbContext.CountryDetails
                                           join csv in masterDbContext.CompanySettingValues
                                               on companyId equals csv.CompanyId
                                           join cs in masterDbContext.CompanySettings
                                               on csv.SettingId equals cs.Id
                                           where cs.SettingKey == "Country"
                                           select cd.TimeZoneOffsetMinutes)
                                          .FirstOrDefaultAsync();

        return TimeSpan.FromMinutes(timeZoneOffsetMinutes);
    }

    private static object ConvertFrom(CompanySettingType settingType, string settingValue, string defaultValue)
    {
        switch (settingType)
        {
            case CompanySettingType.Boolean:
                bool boolValue;
                if (!string.IsNullOrWhiteSpace(settingValue) && bool.TryParse(settingValue, out boolValue))
                    return boolValue;
                return bool.Parse(defaultValue);
            case CompanySettingType.Decimal:
                double decimalValue;
                if (!string.IsNullOrWhiteSpace(settingValue) && double.TryParse(settingValue, out decimalValue))
                    return decimalValue;
                return double.Parse(defaultValue);
            case CompanySettingType.Integer:
                long intValue;
                if (!string.IsNullOrWhiteSpace(settingValue) && long.TryParse(settingValue, out intValue))
                    return intValue;
                return int.Parse(defaultValue);
            case CompanySettingType.TextList:
                if (string.IsNullOrWhiteSpace(settingValue)) return new List<string>();
                var stringlist = JsonSerializer.Deserialize<List<string>>(settingValue);
                return stringlist;
            case CompanySettingType.Text:
                if (string.IsNullOrWhiteSpace(settingValue))
                    return defaultValue;
                return settingValue;
            default:
                return settingValue;
        }
    }
}