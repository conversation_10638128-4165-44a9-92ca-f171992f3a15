﻿using Azure.Identity;
using Microsoft.Extensions.Configuration;

namespace RouteOptimizationProcessors.Tests;
public static class Configuration
{
    public static IConfiguration GetConfiguration()
    {
        var configBuilder = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables();

        var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
        if (!string.IsNullOrEmpty(keyVaultEndpoint))
        {
            //src: https://docs.microsoft.com/en-us/aspnet/core/security/key-vault-configuration?view=aspnetcore-6.0
            configBuilder.AddAzureKeyVault(
                new Uri(keyVaultEndpoint),
                new DefaultAzureCredential()
            );
        }

        return configBuilder.Build();
    }
}
