﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class LiveDataSalesByPosPerspective : IPerspective
{
    public LiveDataSalesByPosPerspective(long companyId)
    {
    }

    public static List<PerspectiveColumnModel> Columns => new()
    {
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L8Position User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L7Position User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L6Position User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L5Position User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L4Position User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L3Position User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L2Position User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L1Position User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L1Position User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Zone",
            Name = "Zone",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Region",
            Name = "Region",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales",
            DisplayName = "Distributor Erp Id",
            Name = "DistributorErpId",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales",
            DisplayName = "Distributor",
            Name = "Distributor",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product Division",
            Name = "ProductDivision",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Primary Category",
            Name = "PrimaryCategory",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Secondary Category",
            Name = "SecondaryCategory",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product",
            Name = "ProductName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product ERPID",
            Name = "ProductERPID",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "SC",
            Name = "SC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "TC",
            Name = "TC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PC",
            Name = "PC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LPC",
            Name = "LPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Lines Cuts",
            Name = "LinesCut_Sales",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( Unit )",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( StdUnit )",
            Name = "OrderQtyInStdUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( SupUnit )",
            Name = "OrderQtyInSupUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Value",
            Name = "Value",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "NetValue",
            Name = "NetValue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        }
    };

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }
}
