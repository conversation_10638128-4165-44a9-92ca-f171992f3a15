﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Library.DateTimeHelpers;
using OfficeOpenXml;

namespace FileGenerator;

public class FileStreamHelper
{
    public static async Task CopyFileStreamToBlobStreamAndCloseBothStream(FilePathAndStream filePathAndStream, Stream blobStream)
    {
        filePathAndStream.Stream.Flush();
        filePathAndStream.Stream.Position = 0;
        await filePathAndStream.Stream.CopyToAsync(blobStream).ConfigureAwait(false);
        // Close the source stream asynchronously
        await filePathAndStream.Stream.DisposeAsync().ConfigureAwait(false);

        if (blobStream != null)
        {
            // Flush the destination stream asynchronously
            await blobStream.FlushAsync().ConfigureAwait(false);
            if (blobStream.CanSeek)
            {
                blobStream.Position = 0;
            }
            else
            {
                // Close the destination stream asynchronously
                await blobStream.DisposeAsync().ConfigureAwait(false);
            }
        }

        // Delete the local temporary file asynchronously
        if (File.Exists(filePathAndStream.Path))
        {
            await Task.Run(() => File.Delete(filePathAndStream.Path)).ConfigureAwait(false);
        }
    }

    public static async Task CopyFileStreamToBlobStream(FilePathAndStream filePathAndStream, Stream blobStream)
    {
        filePathAndStream.Stream.Flush();
        filePathAndStream.Stream.Position = 0;
        await filePathAndStream.Stream.CopyToAsync(blobStream).ConfigureAwait(false);
    }

    public static async Task CopyExcelPackageToBlobStream(ExcelPackage excelPackage, Stream blobStream)
    {
        excelPackage.Save();
        excelPackage.Stream.Flush();
        excelPackage.Stream.Position = 0;
        await excelPackage.Stream.CopyToAsync(blobStream).ConfigureAwait(false);
        // Close the source stream asynchronously
        await excelPackage.Stream.DisposeAsync().ConfigureAwait(false);

        if (blobStream != null)
        {
            // Flush the destination stream asynchronously
            await blobStream.FlushAsync().ConfigureAwait(false);
            if (blobStream.CanSeek)
            {
                blobStream.Position = 0;
            }
            else
            {
                // Close the destination stream asynchronously
                await blobStream.DisposeAsync().ConfigureAwait(false);
            }
        }
    }

    public static void DeleteFileIfExistAndCloseStream(DateAndFile fileDetails)
    {
        // Delete the file if it exists.
        if (File.Exists(fileDetails.Path))
        {
            File.Delete(fileDetails.Path);
        }

        fileDetails.Stream.Close();
    }

    public static void DeleteFileIfExistAndDisposeStream(FilePathAndStream filePathAndStream)
    {
        // Delete the file if it exists.

        filePathAndStream.Stream.Dispose();
        if (File.Exists(filePathAndStream.Path))
        {
            File.Delete(filePathAndStream.Path);
        }
    }

    public static FilePathAndStream GenerateDateAndFileAndStream(string reportId)
    {
        return new FilePathAndStream { Path = $@"{Path.GetTempPath()}{reportId}", Stream = GetFileStream($@"{Path.GetTempPath()}{reportId}") };
    }

    public static FilePathAndStream GenerateDateAndFileAndStream()
    {
        var reportId = Guid.NewGuid().ToString();
        return new FilePathAndStream { Path = $@"{Path.GetTempPath()}{reportId}", Stream = GetFileStream($@"{Path.GetTempPath()}{reportId}") };
    }

    public static List<DateAndFile> GenerateDateAndFileListAndStream(string reportId, DateTime startDate, DateTime endDate)
    {
        var list = new List<DateAndFile>();
        for (var dt = startDate; dt < endDate; dt = dt.AddDays(1))
        {
            list.Add(new DateAndFile { Date = dt, Path = $@"{Path.GetTempPath()}{reportId}_{dt.GetDateKey().ToString()}", Stream = GetFileStream($@"{Path.GetTempPath()}{reportId}_{dt.GetDateKey().ToString()}") });
        }

        return list;
    }

    public static string GenerateFile()
    {
        var reportId = Guid.NewGuid();
        var path = $@"{Path.GetTempPath()}{reportId.ToString()}";
        return path;
    }

    public static string GenerateFile(string reportId)
    {
        var path = $@"{Path.GetTempPath()}{reportId}";
        return path;
    }

    public static FileStream GetFileStream(string path)
    {
        // Delete the file if it exists.
        if (File.Exists(path))
        {
            File.Delete(path);
        }

        //Create the file.
        var fs = File.Create(path);
        return fs;
    }

    public static async Task MergeFiles(List<DateAndFile> fileList, FilePathAndStream localDest, Stream blobStream)
    {
        var fileDest = new StreamWriter(localDest.Stream);

        var isFirst = true;
        foreach (var file in fileList)
        {
            var lines = File.ReadAllLines(file.Path);
            DeleteFileIfExistAndCloseStream(file);

            if (isFirst)
            {
                lines = lines.Skip(1).ToArray(); // Skip header row for all but first file
            }

            foreach (var line in lines)
            {
                await fileDest.WriteLineAsync(line).ConfigureAwait(false);
            }

            isFirst = false;
        }

        localDest.Stream.Flush();
        localDest.Stream.Position = 0;
        localDest.Stream.CopyTo(blobStream);
        localDest.Stream.Close();
        if (blobStream != null)
        {
            blobStream.Flush();
            if (blobStream.CanSeek)
            {
                blobStream.Position = 0;
            }
            else
            {
                blobStream.Close();
            }
        }

        // delete local temp file after wrting to blob
        if (File.Exists(localDest.Path))
        {
            File.Delete(localDest.Path);
        }
    }

    public class DateAndFile : FilePathAndStream
    {
        public DateTime Date { get; set; }
    }

    public class FilePathAndStream
    {
        public string Path { get; set; }
        public FileStream Stream { get; set; }
    }
}
