﻿using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class PositionCodeEntityMappingRepository(MasterDbContext masterDbContext) : IPositionCodeEntityMappingRepository
{
    private IQueryable<PositionCodeEntityMapping> GetActivePositionMapping(long companyId)
    {
        return masterDbContext.PositionCodeEntityMappings
            .Where(p => p.CompanyId == companyId && !p.IsDeactive);
    }

    private IQueryable<PositionCode> GetPositions(long companyId, bool includeDeactive = false)
    {
        if (includeDeactive)
        {
            return masterDbContext.PositionCodes
                .Where(p => p.CompanyId == companyId);
        }
        return masterDbContext.PositionCodes
            .Where(p => p.CompanyId == companyId && !p.Deleted);
    }

    private IQueryable<PositionCode> GetPositionsUnderPositionIds(List<long> positionCodeIds,
        long companyId, bool includeDeactive = false, bool isAdmin = false, bool includeUser = false)
    {
        if (includeUser)
            return GetPositions(companyId, includeDeactive).Where(u =>
                positionCodeIds.Contains(u.Id)
                || positionCodeIds.Contains(u.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Parent.Id)
                || isAdmin);
        return GetPositions(companyId, includeDeactive).Where(u =>
            positionCodeIds.Contains(u.Parent.Id)
            || positionCodeIds.Contains(u.Parent.Parent.Id)
            || positionCodeIds.Contains(u.Parent.Parent.Parent.Id)
            || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Id)
            || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id)
            || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Id)
            || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Parent.Id)
            || isAdmin);
    }

    public async Task<List<PositionCodeEntityMappingMin>> GetAllPositionCodesUnderUserAsync(long companyId, List<long> positionCodeIds, bool isAdmin, bool includeUser)
    {
        return await GetPositionsUnderPositionIds(positionCodeIds, companyId, false, isAdmin, includeUser)
            .Select(x => new PositionCodeEntityMappingMin
            {
                PositionId = x.Id,
                EntityId = x.PositionCodeEntityMappings.Where(m => !m.IsDeactive)
                    .Select(e => e.EntityId).FirstOrDefault(),
                PositionCode = x.Name,
                PositionCodeLevel = x.Level,
                ParentPositionId = x.ParentId
            }).ToListAsync();
    }

    public async Task<List<PositionCodeEntityMappingMin>> GetPositionUsersAsync(long companyId, List<long> pcIds)
    {
        return await GetActivePositionMapping(companyId).Where(p => pcIds.Contains(p.PositionCodeId)).Select(p => new PositionCodeEntityMappingMin
        {
            PositionId = p.PositionCodeId,
            EntityId = p.EntityId,
        }).ToListAsync();
    }
}
