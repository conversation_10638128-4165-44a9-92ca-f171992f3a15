﻿namespace Libraries.CommonEnums;

public enum AchievementDb
{
    Report = 1,
    Transaction = 4,
    Unify = 6,
    MTTransaction = 12,
    BMS = 5
}

public enum AppScreen
{
    FocusRulesScreen = 1,
    MyTargetScreen = 2,
    BeatTargetScreen = 3,
    OutletTargetScreen = 4
}

public enum Heirarchy
{
    Position = 1,
    User = 2,
    Beat = 3,
    Route = 24,
    Outlet = 4,
    Territories = 5,
    Region = 6,
    Zone = 7,
    Product = 8,
    PrimaryCategory = 9,
    SecondaryCategory = 10,
    ProductDivision = 11,
    DisplayCategory = 12,
    Distributors = 13,
    OutletChannel = 14,
    OutletSegmentation = 15,
    ShopTypes = 16,
    DistributorChannel = 17,
    DistributorSegmentation = 18,
    FocusedProductRule = 19,
    MustSellRule = 20
}

public enum TargetType
{
    GTSFA = 0,
    MT = 2,
    RetailerApp = 3,
    DMS = 4
}

public enum TargetFilters
{
    Hierarchy1 = 1,
    Hierarchy2 = 2,
    Hierarchy3 = 3
}

public enum VisualizationType
{
    ProgressBar = 1,
    Speedometer = 2,
    <PERSON>utChart = 3
}
