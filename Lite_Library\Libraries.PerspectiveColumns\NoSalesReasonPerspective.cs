﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class NoSalesReasonPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public NoSalesReasonPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        // Name Property Needs to match the key used for Nomenclature For QuickViz
        new()
        {
            Attribute = "Field User",
            DisplayName = "GSM",
            Name = "GSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Reporting Manager",
            Name = "ReportingManager",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Name",
            Name = "FieldUserName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User ERP ID",
            Name = "FieldUserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "User Designation",
            Name = "UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Mobile Number",
            Name = "FieldUserMobileNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },

        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position,
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position,
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position,
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position,
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position,
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position,
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position,
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position,
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Reporting Manager",
            Name = "L2Position_ReportingUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser Name",
            Name = "L1Position_FieldUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Field User ERP ID",
            Name = "L1Position_UserERP",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "User Designation",
            Name = "L1Position_UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser Mobile Number",
            Name = "L1Position_UserMobileNumber",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText1,
            Name = "EmployeeAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText2,
            Name = "EmployeeAttributeText2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber1,
            Name = "EmployeeAttributeNumber1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber2,
            Name = "EmployeeAttributeNumber2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Date Of Joining",
            Name = "DateOfJoining",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Email Id",
            Name = "EmailId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "User Type",
            Name = "UserType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Contact Number",
            Name = "ContactNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Visit",
            DisplayName = "Unproductive Visits",
            Name = "AttendanceId",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Distributor And Stockist",
            DisplayName = linkNames.SuperStockist,
            Name = "SuperStockist",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = false
        },
        new()
        {
            Attribute = "Distributor And Stockist",
            DisplayName = linkNames.SuperStockist + " Erp Id",
            Name = "SuperStockistErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = false
        },
        new()
        {
            Attribute = "Distributor And Stockist",
            DisplayName = linkNames.Distributor,
            Name = "Distributor",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Distributor And Stockist",
            DisplayName = linkNames.Distributor + " Erp Id",
            Name = "DistributorErpId",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Distributor And Stockist",
            DisplayName = "Invoice Status",
            Name = "InvoiceStatus",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat,
            Name = "Beat",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat + " ERP Id",
            Name = "BeatErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat + " " + linkNames.AttributeText1,
            Name = "BeatAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Territory,
            Name = "Territory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true,
            IsPivot = false
        },
        // Name Property Of New Geographies need to be in this Pattern (LevelX) only or Filtering based on setting won't work
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level5,
            Name = "Level5",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level6,
            Name = "Level6",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level7,
            Name = "Level7",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Route,
            Name = "Route",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Route + " Erp Id",
            Name = "RouteErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "FOC",
            Name = "SchemeQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Scheme Discount",
            Name = "SchemeDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Discount",
            Name = "ProductWiseDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Value",
            Name = "Value",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Drop Size",
            Name = "AvgValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "NetValue",
            Name = "NetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.Unit})",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.StdUnit})",
            Name = "OrderQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.SuperUnit})",
            Name = "OrderQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Shipped Order Qty ({linkNames.Unit})",
            Name = "ShippedOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Shipped Order Qty ({linkNames.StdUnit})",
            Name = "ShippedOrderQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Shipped Order Qty ({linkNames.SuperUnit})",
            Name = "ShippedOrderQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Retailer Return Value",
            Name = "RetailerReturnInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Retailer Return Qty ({linkNames.Unit})",
            Name = "RetailerReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Retailer Return Qty ({linkNames.StdUnit})",
            Name = "RetailerReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Retailer Return Qty ({linkNames.SuperUnit})",
            Name = "RetailerReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Shipped Order Value",
            Name = "ShippedOrderValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Retailer Stock Value",
            Name = "RetailerStockInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Retailer Stock Qty ({linkNames.Unit})",
            Name = "RetailerStockQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Retailer Stock Qty ({linkNames.StdUnit})",
            Name = "RetailerStockInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Net Value (Dispatch)",
            Name = "DispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Dispatch Qty ({linkNames.Unit})",
            Name = "DispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Dispatch Qty ({linkNames.StdUnit})",
            Name = "DispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = $"Dispatch Qty ({linkNames.SuperUnit})",
            Name = "DispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Dispatch Status",
            Name = "DispatchStatus",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "No Sales Reason",
            Name = "NoSalesReason",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "No Sales Reason Category",
            Name = "NoSalesReasonCategory",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Productive Visits",
            Name = "ProductiveVisits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "No Sale Visits",
            Name = "NoSaleVisits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Visits",
            Name = "Visits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "UTC",
            Name = "UTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "UPC",
            Name = "UPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "FA Unify Source",
            Name = "FAUnifySource",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "OVC TC",
            Name = "OVCTC",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "OVC PC",
            Name = "OVCPC",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.OVT + " TC ",
            Name = "OVTTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.OVT + " PC ",
            Name = "OVTPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Time Spent At Outlet",
            Name = "CallDuration",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Time",
            Name = "Time",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Date",
            Name = "Date",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Week",
            Name = "Week",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Telephonic Validated Calls",
            Name = "TelephonicValidated",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets,
            Name = "Shop",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "ErpId",
            Name = "ShopErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "OwnersName",
            Name = "ShopOwnersName",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "OwnersNo",
            Name = "ShopOwnersNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "Address",
            Name = "ShopAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "Market",
            Name = "ShopMarket",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "Town",
            Name = "ShopSubCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "City",
            Name = "ShopCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "State",
            Name = "ShopState",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "Type",
            Name = "ShopType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + "Segmentation",
            Name = "ShopSegmentation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = "Focussed" + linkNames.Outlets,
            Name = "FocussedShop",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Shop",
            DisplayName = linkNames.Outlets + " Channel",
            Name = "ShopChannel",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Master Measure",
            DisplayName = "Focused Outlet Target",
            Name = "FocusedOutletTarget",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        }
    };
}
