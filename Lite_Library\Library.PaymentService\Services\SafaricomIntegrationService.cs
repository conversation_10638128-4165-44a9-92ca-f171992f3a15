﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Net;
using System.Net.Http.Headers;
using System.Text;
using Library.PaymentService.Models;
using Newtonsoft.Json;

namespace Library.PaymentService.Services
{
    public class SafaricomIntegrationService
    {
        private readonly string _consumerKey;
        private readonly string _consumerSecret;
        private readonly string _baseUrl;
        private readonly IHttpClientFactory _httpClientFactory; // Added IHttpClientFactory field

        public SafaricomIntegrationService(string baseUrl, string consumerKey, string consumerSecret, IHttpClientFactory httpClientFactory) // Modified constructor to accept IHttpClientFactory
        {
            _baseUrl = baseUrl;
            _consumerKey = consumerKey;
            _consumerSecret = consumerSecret;
            _httpClientFactory = httpClientFactory; // Initialize the IHttpClientFactory field
        }

        private async Task<string?> GetToken()
        {
            var plainTextBytes = Encoding.UTF8.GetBytes($"{_consumerKey}:{_consumerSecret}");
            var auth = Convert.ToBase64String(plainTextBytes);

            var client = _httpClientFactory.CreateClient(); // Correctly using _httpClientFactory to create an HttpClient
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", auth);

            var url = $"{_baseUrl}/oauth/v1/generate?grant_type=client_credentials";
            var res = await client.GetAsync(url);

            res.EnsureSuccessStatusCode();

            var data = JsonConvert.DeserializeObject<SafaicomAuthentication>(await res.Content.ReadAsStringAsync());
            return data?.access_token;
        }

        public async Task<STKPushResponse?> RequestPayment(SafaricomPaymentRequest request)
        {
            var auth = await GetToken();
            var client = _httpClientFactory.CreateClient(); // Correctly using _httpClientFactory to create an HttpClient

            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", auth); // Setting the Authorization header correctly

            var jsonRequest = JsonConvert.SerializeObject(request);
            var buffer = Encoding.UTF8.GetBytes(jsonRequest);
            var byteContent = new ByteArrayContent(buffer);
            byteContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var url = $"{_baseUrl}/mpesa/stkpush/v1/processrequest";
            var data = await client.PostAsync(url, byteContent); // Correctly using HttpClient instance to make a POST request

            var response = new STKPushResponse();
            try
            {   // ensure success      
                data.EnsureSuccessStatusCode();

                var responseContent = await data.Content.ReadAsStringAsync();
                response.StatusCode = HttpStatusCode.BadRequest;
                response.SuccessResponse = JsonConvert.DeserializeObject<SuccessResponse>(responseContent);
            }
            catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.BadRequest)
            {
                // handle bad request
                var responseContent = await data.Content.ReadAsStringAsync();
                response.StatusCode = HttpStatusCode.BadRequest;
                response.FailureResponse = JsonConvert.DeserializeObject<FailureResponse>(responseContent);
            }
            catch (Exception ex)
            {
                // Catch any other exceptions and handle them
                throw;
            }

            return response;
        }
    }
}
