using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class EmployeeTourPlanRepository(WritableMasterDbContext writableMasterDbContext, 
    MasterDbContext masterDbContext) : IEmployeeTourPlanRepository
{
    public async Task SaveEmployeeTourPlan(List<AutomaticDJPDbModel> records, long companyId, DateTime startDate, DateTime currentDate,
        List<string> possibleWeekOffStrings,
        bool usesPositionCodes,
        CancellationToken ct = default)
    {
        // Get employee position code mappings if using position codes
        var employeePositionMappings = new Dictionary<long, long>();
        if (usesPositionCodes)
        {
            var employeeIds = records.Select(r => r.EmployeeId).Distinct().ToList();
            var positionMappings = await masterDbContext.PositionCodeEntityMappings
                .Where(p => p.CompanyId == companyId && !p.IsDeactive && employeeIds.Contains(p.EntityId))
                .Select(p => new { p.EntityId, p.PositionCodeId })
                .ToListAsync(ct);

            employeePositionMappings = positionMappings.ToDictionary(p => p.EntityId, p => p.PositionCodeId);
        }

        // Group into batches of 10 employees
        var employeeRoutesBatch = records
            .GroupBy(s => s.EmployeeId)
            .Select((group, index) => new
            {
                EmployeeId = group.Key,
                Items = group.ToList(),
                BatchIndex = index / 10
            })
            .GroupBy(x => x.BatchIndex)
            .ToList();

        foreach (var batch in employeeRoutesBatch)
        {
            await using var transaction = await writableMasterDbContext.Database.BeginTransactionAsync(ct);
            try
            {
                foreach (var employeeRoutes in batch)
                {
                    var empErpId = employeeRoutes.Items.First().EmployeeErpId;
                    var positionCodeId = usesPositionCodes && employeePositionMappings.TryGetValue(employeeRoutes.EmployeeId, out var posId) ? posId : (long?)null;

                    // Create master tour plan
                    var employeeTourPlan = new EmployeeTourPlan
                    {
                        EmployeeId = employeeRoutes.EmployeeId,
                        CompanyId = companyId,
                        StartDate = startDate,
                        EffectiveStartDate = startDate,
                        DeviceTime = DateTime.UtcNow,
                        ServerTime = DateTime.UtcNow,
                        EndDate = startDate.AddDays(7),
                        ReviewedStatus = TourPlanApprovedStatus.Pending,
                        IsRepeatable = false,
                        ForDays = 7,
                        PositionCodeId = positionCodeId,
                        EmployeeTourPlanItems = []
                    };

                    // Group by date → skip weekly offs
                    var dateWiseEmpRoutes = employeeRoutes.Items
                        .GroupBy(s => s.Date)
                        .ToDictionary(
                            gr => gr.Key,
                            gr => gr.GroupBy(r => r.OutletId).Select(q => q.First()).ToList()
                        );

                    // Track routes created for this employee
                    var routeCreationTasks = new List<Task<long>>();
                    var routeIdMapping = new Dictionary<string, long>();

                    foreach (var dateWiseRoutes in dateWiseEmpRoutes)
                    {
                        var isWeeklyOff = dateWiseRoutes.Value.Exists(s =>
                            possibleWeekOffStrings.Contains(s.OutletErpId));

                        if (isWeeklyOff)
                        {
                            employeeTourPlan.EmployeeTourPlanItems.Add(new EmployeeTourPlanItem
                            {
                                ItemDate = dateWiseRoutes.Key,
                                CompanyId = companyId,
                                ReasonCategory = "Weekly Off"
                            });
                            continue;
                        }

                        // Create route for this date if outlets exist
                        var outletsForDate = dateWiseRoutes.Value.Where(r => r.OutletId > 0).ToList();
                        if (outletsForDate.Any())
                        {
                            var routeKey = $"{empErpId}_{dateWiseRoutes.Key:yyyyMMdd}";
                            long routeId;

                            if (!routeIdMapping.TryGetValue(routeKey, out routeId))
                            {
                                // Create new route
                                var routeName = $"Route_{empErpId}_{dateWiseRoutes.Key:yyyyMMdd}";
                                var routeOutletMappings = outletsForDate
                                    .OrderBy(r => r.Sequence)
                                    .Select((outlet, index) => new RouteOutletMapping
                                    {
                                        LocationId = outlet.OutletId,
                                        CompanyId = companyId,
                                        VisitOrder = index + 1,
                                        CreatedAt = DateTime.UtcNow,
                                        LastUpdatedAt = DateTime.UtcNow,
                                        CreationContext = "ro-playground"
                                    }).ToList();

                                var route = new Route
                                {
                                    Name = routeName,
                                    ErpId = routeName,
                                    CompanyId = companyId,
                                    IsDeactive = false,
                                    RouteOutletMappings = routeOutletMappings,
                                    RouteGuid = Guid.NewGuid(),
                                    CreatedAt = DateTime.UtcNow,
                                    LastUpdatedAt = DateTime.UtcNow,
                                    CreationContext = "ro-playground"
                                };

                                await writableMasterDbContext.Routes.AddAsync(route, ct);
                                await writableMasterDbContext.SaveChangesAsync(ct);
                                routeId = route.Id;
                                routeIdMapping[routeKey] = routeId;

                                // Create route mappings based on company configuration
                                if (usesPositionCodes && positionCodeId.HasValue)
                                {
                                    // Create route position mapping
                                    var routePositionMapping = new RoutePositionMapping
                                    {
                                        RouteId = routeId,
                                        PositionCodeId = positionCodeId.Value,
                                        CompanyId = companyId,
                                        CreatedAt = DateTime.UtcNow,
                                        LastUpdatedAt = DateTime.UtcNow,
                                        CreationContext = "ro-playground"
                                    };
                                    await writableMasterDbContext.RoutePositionMappings.AddAsync(routePositionMapping, ct);
                                }
                                else
                                {
                                    // Create employee route mapping
                                    var employeeRouteMapping = new EmployeeRouteMapping
                                    {
                                        EmployeeId = employeeRoutes.EmployeeId,
                                        RouteId = routeId,
                                        CompanyId = companyId,
                                        TimeAdded = DateTime.UtcNow,
                                        CreatedAt = DateTime.UtcNow,
                                        LastUpdatedAt = DateTime.UtcNow,
                                        CreationContext = "AutomaticDJP"
                                    };
                                    await writableMasterDbContext.FAEmployeeRouteMappings.AddAsync(employeeRouteMapping, ct);
                                }
                            }

                            // Add tour plan item with route
                            employeeTourPlan.EmployeeTourPlanItems.Add(new EmployeeTourPlanItem
                            {
                                ItemDate = dateWiseRoutes.Key,
                                CompanyId = companyId,
                                RouteId = routeId,
                                ReasonCategory = "Retailing",
                                JWFieldUserId = employeeRoutes.EmployeeId,
                                JWFieldUserPositionId = positionCodeId
                            });
                        }
                    }

                    // Save tour plan + its items
                    await writableMasterDbContext.EmployeeTourPlans.AddAsync(employeeTourPlan, ct);
                }

                await writableMasterDbContext.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
    }
}

