using Libraries.CommonEnums;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;
public class EmployeeTourPlanRepository(WritableMasterDbContext writableMasterDbContext) : IEmployeeTourPlanRepository
{
    public async Task SaveEmployeeTourPlan(List<AutomaticDJPDbModel> records, long companyId, DateTime startDate, DateTime currentDate, 
        List<string> possibleWeekOffStrings,
        bool usesPositionCodes,
        CancellationToken ct = default)
    {
        // Group into batches of 10 employees
        var employeeRoutesBatch = records
            .GroupBy(s => s.EmployeeId)
            .Select((group, index) => new
            {
                EmployeeId = group.Key,
                Items = group.ToList(),
                BatchIndex = index / 10
            })
            .GroupBy(x => x.BatchIndex)
            .ToList();

        foreach (var batch in employeeRoutesBatch)
        {
            await using var transaction = await writableMasterDbContext.Database.BeginTransactionAsync(ct);
            try
            {
                foreach (var employeeRoutes in batch)
                {
                    var empErpId = employeeRoutes.Items.First().EmployeeErpId;

                    // Create master tour plan
                    var employeeTourPlan = new EmployeeTourPlan
                    {
                        EmployeeId = employeeRoutes.EmployeeId,
                        CompanyId = companyId,
                        StartDate = startDate,
                        EffectiveStartDate = startDate,
                        DeviceTime = DateTime.UtcNow,
                        ServerTime = DateTime.UtcNow,
                        EndDate =
                        ReviewedStatus = TourPlanApprovedStatus.Pending,
                        IsRepeatable = false,
                        ForDays = 7,
                        PositionCodeId = usesPositionCodes ? employeeRoutes.Items.FirstOrDefault()?.PositionCodeId : null,
                        EmployeeTourPlanItems = new List<EmployeeTourPlanItem>()
                        {
                            
                        }
                    };

                    // Group by date → skip weekly offs
                    var dateWiseEmpRoutes = employeeRoutes.Items
                        .GroupBy(s => s.Date)
                        .ToDictionary(
                            gr => gr.Key,
                            gr => gr.GroupBy(r => r.OutletId).Select(q => q.First()).ToList()
                        );

                    foreach (var dateWiseRoutes in dateWiseEmpRoutes)
                    {
                        var isWeeklyOff = dateWiseRoutes.Value.Exists(s =>
                            possibleWeekOffStrings.Contains(s.OutletErpId));

                        if (isWeeklyOff)
                        {
                            employeeTourPlan.EmployeeTourPlanItems.Add(new EmployeeTourPlanItem
                            {
                                ItemDate = dateWiseRoutes.Key,
                                CompanyId = companyId,
                                ReasonCategory = "Weekly Off"
                            });
                            continue;
                        }

                        foreach (var route in dateWiseRoutes.Value)
                        {
                            employeeTourPlan.EmployeeTourPlanItems.Add(new EmployeeTourPlanItem
                            {
                                ItemDate = dateWiseRoutes.Key,
                                CompanyId = companyId,
                                RouteId = route.RouteId, // You can map if available
                                ReasonCategory = "Retailing"
                            });
                        }
                    }

                    // Save tour plan + its items
                    await writableMasterDbContext.EmployeeTourPlans.AddAsync(employeeTourPlan, ct);
                }

                await writableMasterDbContext.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
    }
}

