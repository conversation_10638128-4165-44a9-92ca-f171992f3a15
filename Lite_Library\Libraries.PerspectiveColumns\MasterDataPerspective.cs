﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

internal sealed class MasterDataPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public MasterDataPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager,
            Name = "GSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.Employee,
            Name = "ESM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Field User Name",
            Name = "FieldUserName",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "User Designation",
            Name = "UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales",
            DisplayName = linkNames.Distributor,
            Name = "Distributor",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat,
            Name = "Beat",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        //new PerspectiveColumnModel() { Attribute = "Sales Territory" ,  DisplayName = linkNames.Region ,                Name = "Region",         IsMeasure = true,     IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
        new PerspectiveColumnModel
        {
            Attribute = "Outlets",
            DisplayName = linkNames.Outlets,
            Name = "Shop",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        }
    };

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }
}
