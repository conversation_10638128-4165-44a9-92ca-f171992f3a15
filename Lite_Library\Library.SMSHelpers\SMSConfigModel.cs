﻿using System;
using Libraries.CommonEnums;

namespace Library.SMSHelpers;

public class SMSConfiguration
{
    public SMSConfiguration()
    {
        Message = Messages
            .OrderDefaultMessage; // "Dear {0} Thanks for placing order with {5} for {1} {4} on {2}. Your order id is {3} .";
        UnProductiveMessage =
            Messages.OrderUnProductiveMessage; // "Thanks for your valuable time. We wish to serve you next time. Regards {0}";
        OTPMessage = Messages.OtpDefaultMessage;
    }

    public long Id { get; set; }

    public long CompanyId { get; set; }
    public OrderKind OrderKind { get; set; }

    public string CompanyName { get; set; }
    public string Url { get; set; }
    public bool SendOnlyProductive { get; set; }
    public string Message { get; set; }
    public string UnProductiveMessage { get; set; }
    public string OTPMessage { get; set; }
    public bool IsPOSTOnly { get; set; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool IsDeactive { get; set; }

    public SMSConfiguration ToLiberaryModel()
    {
        return new SMSConfiguration
        {
            CompanyId = CompanyId,
            CompanyName = CompanyName,
            CreatedAt = CreatedAt,
            CreationContext = CreationContext,
            Id = Id,
            IsDeactive = IsDeactive,
            IsPOSTOnly = IsPOSTOnly,
            LastUpdatedAt = LastUpdatedAt,
            Message = Message,
            OrderKind = OrderKind,
            OTPMessage = OTPMessage,
            SendOnlyProductive = SendOnlyProductive,
            UnProductiveMessage = UnProductiveMessage,
            Url = Url
        };
    }
}

public static class Messages
{
    public const string ActivationCodeMessage =
        "As per your request your Activation Code is {0}. Use it to activate the app.";

    public const string OrderDefaultMessage =
        "Dear {0} Thanks for placing order with {5} for {1} {4} on {2}. Your order id is {3} .";

    public const string OrderUnProductiveMessage =
        "Thanks for your valuable time. We wish to serve you next time. Regards {0}";

    public const string OtpDefaultMessage =
        "Dear {0}, to register yourself with {1}, please share the OTP {2} with company {3}";

    public const string OtpFirstOrderMessage =
        "Dear {0} Thanks for registering with {5}. Your order has been placed for {1} {4} on {2}.Your order id is {3} .";

    public const string OtpOutletCreationMessage =
        "Dear {0}, to register yourself with {1}, please share the Welcome Message Number {2} with {3}";

    public const string OutletCreationConfirmation =
        "Dear Partner, Thank you for Verification. We are happy to serve you. ";

    public const string TAProductiveMessage =
        "Dear {0} Thanks for placing order with {5} for {10} {4} of {9} {1} on {2}. Your order id is {3} . Tgt - {6} {4}, Achievement till Yesterday -{7} {4}";

    public const string TAUnpPoductiveMessage =
        "Thanks for your valuable time.We wish to serve you next time.Regards {0}. Tgt - {2} {1}, Achievement till Yesterday - {3} {1}";
}
