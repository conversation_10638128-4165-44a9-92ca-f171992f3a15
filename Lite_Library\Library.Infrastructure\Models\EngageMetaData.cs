﻿using System;
using Libraries.CommonEnums;

namespace Library.Infrastructure.Models;

public class EngageMetaData
{
    public Guid id { get; set; }
    public Guid transactionId { get; set; }
    public long? alertId { get; set; }
    public string title { get; set; }
    public string message { get; set; }
    public AppNotificationType placement { get; set; }
    public int? inAppScreen { get; set; }
    public string actionLink { get; set; }
    public int? actionScreen { get; set; }
    public string emoji { get; set; }
}
