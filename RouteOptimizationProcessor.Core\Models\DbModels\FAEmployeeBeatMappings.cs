﻿using System.ComponentModel.DataAnnotations.Schema;

namespace RouteOptimizationProcessor.Core.Models.DbModels;

public class FAEmployeeBeatMappings
{
    public long Id { get; set; }
    [ForeignKey("ClientEmployee")]
    public long EmployeeId { get; set; }

    [ForeignKey("LocationBeat")]
    public long BeatId { get; set; }

    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }

    [ForeignKey("Company")]
    public long CompanyId { get; set; }
    public string? CreationContext { get; set; }
    public bool IsDeleted { get; set; }
    public bool IsTemporaryAttachment { get; set; }

    public virtual ClientEmployee ClientEmployee { get; set; }
}