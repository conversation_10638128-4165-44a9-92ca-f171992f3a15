﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum ImageContainers
{
    [Display(Name = "outletimages")] OutletImages,
    [Display(Name = "surveyimages")] SurveyImages,
    [Display(Name = "selfieimages")] SelfieImage,
    [Display(Name = "campaignresponse")] CampaignResponse,
    [Display(Name = "stockinwardimages")] StockInwardImages,

    [Display(Name = "paymentimages")] PaymentImages,

    [Display(Name = "invoiceimages")] InvoiceImages,

    [Display(Name = "daystartimage")] DayStartImage,

    [Display(Name = "ocrimages")] OcrImage,
    [Display(Name = "distributorimages")] DistributorImages,
    [Display(Name = "assetimages")] AssetImages,

    [Display(Name = "companyimages")] CompanyImages,

    [Display(Name = "productimages")] ProductImages,

    [Display(Name = "carouselbanners")] CarouselBanners,

    [Display(Name = "userprofilepicture")] UserProfilePicture,

    [Display(Name = "scenes")] Scenes
}
