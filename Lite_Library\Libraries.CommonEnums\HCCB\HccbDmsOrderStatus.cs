﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums.HCCB;

public enum HccbDmsOrderStatus
{
    [Display(Name = "Received")]
    Received = 0,

    [Display(Name = "Cancelled")]
    Cancelled = 2,

    [Display(Name = "Invoiced")]
    Invoiced = 3,

    [Display(Name = "Load Out Completed")]
    Load_Out_Completed = 4,

    [Display(Name = "SO_DN_Completed")]
    So_Dn_Completed = 5,

    [Display(Name = "Expired")]
    Expired = 6,

    [Display(Name = "Not Found")]
    NotFound = 10
}
