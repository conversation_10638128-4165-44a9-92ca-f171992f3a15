﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums.Helpers;

public enum ColumnType
{
    Master,
    Measure,
    Dimension,
    Fact
}

public enum DailyDataPointers
{
    [Display(Name = "Total Users", GroupName = "Fixed", Order = 1)]
    TotalUsers,

    [Display(Name = "Retailing", GroupName = "Fixed", Order = 2)]
    Retailing,

    [Display(Name = "Official Work", GroupName = "Fixed", Order = 3)]
    OfficialWork,

    [Display(Name = "Leave", GroupName = "Fixed", Order = 4)]
    Leave,

    [Display(Name = "Planned Leave", GroupName = "Fixed", Order = 5)]
    PlannedLeave,

    [Display(Name = "Absent", GroupName = "Fixed", Order = 6)]
    Absent,

    [Display(Name = "Reporting Manager", GroupName = "Fixed", Order = 7)]
    ReportingManager,

    [Display(Name = "Field User", GroupName = "Fixed", Order = 8)]
    FieldUserName,

    [Display(Name = "Field User HQ", GroupName = "Fixed", Order = 9)]
    FieldUserHQ,

    [Display(Name = "Rank", GroupName = "Fixed", Order = 10)]
    FieldUserRole,

    [Display(Name = "Assigned Type", GroupName = "Fixed", Order = 11)]
    AssignedReasonCategory,

    [Display(Name = "Type", GroupName = "Fixed", Order = 12)]
    ReasonCategory,

    [Display(Name = "Assigned Reason", GroupName = "Fixed", Order = 13)]
    AssignedReason,

    [Display(Name = "Reason", GroupName = "Fixed", Order = 14)]
    Reason,

    [Display(Name = "Assigned JW User", GroupName = "Fixed", Order = 15)]
    AssignedJointWorkingEmployee,

    [Display(Name = "Selected JW User", GroupName = "Fixed", Order = 16)]
    JointWorkingEmployee,

    [Display(Name = "Assigned Beat", GroupName = "Fixed", Order = 17)]
    AssignedBeat,

    [Display(Name = "Selected Beat", GroupName = "Fixed", Order = 18)]
    SelectedBeat,

    [Display(Name = "Assigned Route", GroupName = "Fixed", Order = 17)]
    AssignedRoute,

    [Display(Name = "Selected Route", GroupName = "Fixed", Order = 18)]
    SelectedRoute,

    [Display(Name = "Distributor", GroupName = "Fixed", Order = 19)]
    Distributor,

    [Display(Name = "Day Start Location", GroupName = "Fixed", Order = 20)]
    DayStartLocation,

    [Display(Name = "Log In", GroupName = "NotFixed", Order = 21)]
    Login,

    [Display(Name = "First Call", GroupName = "NotFixed", Order = 22)]
    FirstCallTime,

    [Display(Name = "First PC", GroupName = "NotFixed", Order = 23)]
    FirstPCTime,

    [Display(Name = "SC", GroupName = "NotFixed", Order = 24)]
    SC,

    [Display(Name = "TC", GroupName = "NotFixed", Order = 25)]
    TC,

    [Display(Name = "PC", GroupName = "NotFixed", Order = 26)]
    PC,

    [Display(Name = "Productivity(%)", GroupName = "NotFixed", Order = 26)]
    Productivity,

    [Display(Name = "SchProductivity(%)", GroupName = "NotFixed", Order = 26)]
    SchProductivity,

    [Display(Name = "CAP", GroupName = "NotFixed", Order = 27)]
    CAP,

    [Display(Name = "OVT (%)", GroupName = "NotFixed", Order = 28)]
    OVT,

    [Display(Name = "OVC (%)", GroupName = "NotFixed", Order = 29)]
    OVC,

    [Display(Name = "TO", GroupName = "NotFixed", Order = 30)]
    TelephonicOrders,

    [Display(Name = "JW Calls", GroupName = "NotFixed", Order = 31)]
    JointWorkingCalls,

    [Display(Name = "Qty (Std Unit)", GroupName = "NotFixed", Order = 32)]
    OrderInStdUnits,

    [Display(Name = "FOC", GroupName = "NotFixed", Order = 33)]
    TotalSchemeQty,

    [Display(Name = "Net Value", GroupName = "NotFixed", Order = 34)]
    NetValue,

    [Display(Name = "New Outlets", GroupName = "NotFixed", Order = 35)]
    NewOutletsCreated,

    [Display(Name = "No Of Other Activities", GroupName = "NotFixed", Order = 36)]
    NoOfOtherActivities,

    [Display(Name = "MTD", GroupName = "NotFixed", Order = 37)]
    MTDValue,

    [Display(Name = "MTD", GroupName = "NotFixed", Order = 38)]
    MTDUnit,

    [Display(Name = "Physical TC", GroupName = "NotFixed", Order = 39)]
    PhycialCalls,

    [Display(Name = "DayStartType", GroupName = "NotFixed", Order = 40)]
    DayStartType,

    [Display(Name = "DayStartReasonCategory", GroupName = "NotFixed", Order = 41)]
    DayStartReasonCategory,

    [Display(Name = "DayStartReasonDescription", GroupName = "NotFixed", Order = 42)]
    DayStartReasonDescription,

    [Display(Name = "Total Time", GroupName = "NotFixed", Order = 43)]
    TotalTime,

    [Display(Name = "Is Journey Violated", GroupName = "NotFixed", Order = 44)]
    IsJourneyViolated,

    [Display(Name = "New Outlet Value", GroupName = "NotFixed", Order = 45)]
    NewOutletSalesInRevenue,

    [Display(Name = "Selected Journey Outlet", GroupName = "NotFixed", Order = 46)]
    SelectedJourneyOutlet,

    [Display(Name = "Telephonic Order Value", GroupName = "NotFixed", Order = 47)]
    TelephonicOrdersValue,

    [Display(Name = "Qty (Super Unit)", GroupName = "NotFixed", Order = 48)]
    OrderInSuperUnits,

    [Display(Name = "Is Position Not UnderManager ", GroupName = "NotFixed", Order = 47)]
    IsPositionNotUnderManager,

    [Display(Name = "UPC", GroupName = "NotFixed", Order = 48)]
    UPC,

    [Display(Name = "UTC", GroupName = "NotFixed", Order = 49)]
    UTC,

    [Display(Name = "Journey diverted days", GroupName = "NotFixed", Order = 50)]
    JourneyDivertedDays,

    [Display(Name = "JW PC ", GroupName = "NotFixed", Order = 51)]
    JWPC,

    [Display(Name = "JW OVC ", GroupName = "NotFixed", Order = 52)]
    JWOVC,

    [Display(Name = "JW OVT ", GroupName = "NotFixed", Order = 53)]
    JWOVT,

    [Display(Name = "JW Sales (Unit) ", GroupName = "NotFixed", Order = 54)]
    JWSalesUnit,

    [Display(Name = "JW Sales (Std. Unit) ", GroupName = "NotFixed", Order = 55)]
    JWSalesStdUnit,

    [Display(Name = "JW Sales Value ", GroupName = "NotFixed", Order = 56)]
    JWSalesValue,

    [Display(Name = "LPC ", GroupName = "NotFixed", Order = 57)]
    LPC,

    [Display(Name = "ULC ", GroupName = "NotFixed", Order = 58)]
    ULC,

    [Display(Name = "Fill rate%", GroupName = "NotFixed", Order = 59)]
    FillratePer,

    [Display(Name = "Dispatched calls (DC)", GroupName = "NotFixed", Order = 60)]
    DC,

    [Display(Name = "PW Scheduled Visits", GroupName = "NotFixed", Order = 61)]
    PWScheduledVisits,

    [Display(Name = "Total PW Visits", GroupName = "NotFixed", Order = 62)]
    TotalPWVisits,

    [Display(Name = "Primary Working Productive Visits", GroupName = "NotFixed", Order = 63)]
    TotalPWProductiveVisits,

    [Display(Name = "Avg PLPC", GroupName = "NotFixed", Order = 64)]
    AvgPLPC,

    [Display(Name = "Primary Working OVC %", GroupName = "NotFixed", Order = 65)]
    PWOVCPercentage,

    [Display(Name = "Average Time Spent", GroupName = "NotFixed", Order = 66)]
    PWAvgTimeSpent,

    [Display(Name = "Total Primary Working JW Sessions", GroupName = "NotFixed", Order = 66)]
    PWJWSessions,

    [Display(Name = "Net Value With Tax", GroupName = "NotFixed", Order = 67)]
    NetValueWithTax,

    [Display(Name = "Log out time", GroupName = "NotFixed", Order = 68)]
    LogOutTime,

    [Display(Name = "CAP adherance %", GroupName = "NotFixed", Order = 69)]
    CAPAdherancePer
}

public enum PointerState
{
    Unknown = 0,
    Fixed = 1,
    Default = 2,
    Other = 4,
    CheckedForManagers = 8,
    CheckedForUsers = 16,
    CheckedForDashboard = 24
}

public class ManagerAppDataPointer
{
    private readonly LinkNames linkNames;

    public ManagerAppDataPointer()
    {
        linkNames = LinkNames.GetLinkNames(new Dictionary<string, string>());
    }

    public ManagerAppDataPointer(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    // Manager App Pointers as per old dashboard
    public List<ManagerAppPointers> AllPointers => new List<ManagerAppPointers>
    {
        new(PointerState.Fixed)
        {
            DisplayName = "Total Users",
            Name = DailyDataPointers.TotalUsers,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Retailing",
            Name = DailyDataPointers.Retailing,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Official Work",
            Name = DailyDataPointers.OfficialWork,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Leave",
            Name = DailyDataPointers.Leave,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Planned Leave",
            Name = DailyDataPointers.PlannedLeave,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Absent",
            Name = DailyDataPointers.Absent,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Reporting Manager",
            Name = DailyDataPointers.ReportingManager,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
        {
            DisplayName = "Field User",
            Name = DailyDataPointers.FieldUserName,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Field User HQ",
            Name = DailyDataPointers.FieldUserHQ,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Rank",
            Name = DailyDataPointers.FieldUserRole,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Assigned Type",
            Name = DailyDataPointers.AssignedReasonCategory,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
        {
            DisplayName = "Type",
            Name = DailyDataPointers.ReasonCategory,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Assigned Reason",
            Name = DailyDataPointers.AssignedReason,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
        {
            DisplayName = "Reason",
            Name = DailyDataPointers.Reason,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Assigned JW User",
            Name = DailyDataPointers.AssignedJointWorkingEmployee,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
        {
            DisplayName = "Selected JW User",
            Name = DailyDataPointers.JointWorkingEmployee,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Assigned " + linkNames.Beat,
            Name = DailyDataPointers.AssignedBeat,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
        {
            DisplayName = "Selected " + linkNames.Beat,
            Name = DailyDataPointers.SelectedBeat,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Assigned Route",
            Name = DailyDataPointers.AssignedRoute,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
        {
            DisplayName = "Selected Route",
            Name = DailyDataPointers.SelectedRoute,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = linkNames.Distributor,
            Name = DailyDataPointers.Distributor,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = "Day Start Location",
            Name = DailyDataPointers.DayStartLocation,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "Login",
            Name = DailyDataPointers.Login,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "First Call",
            Name = DailyDataPointers.FirstCallTime,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "First PC",
            Name = DailyDataPointers.FirstPCTime,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "SC",
            Name = DailyDataPointers.SC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "TC",
            Name = DailyDataPointers.TC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "CAP",
            Name = DailyDataPointers.CAP,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "PC",
            Name = DailyDataPointers.PC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "Productivity(%)",
            Name = DailyDataPointers.Productivity,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        //new ManagerAppPointers(PointerState.Other) {DisplayName = "Productivity(%)",Name = DailyDataPointers.Productivity", IsForManager = true},
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "Sch. Productivity(%)",
            Name = DailyDataPointers.SchProductivity,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "OVT (%)",
            Name = DailyDataPointers.OVT,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "OVC (%)",
            Name = DailyDataPointers.OVC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW Calls",
            Name = DailyDataPointers.JointWorkingCalls,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "Qty (Std Unit)",
            Name = DailyDataPointers.OrderInStdUnits,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other, PointerState.Default)
        {
            DisplayName = "FOC",
            Name = DailyDataPointers.TotalSchemeQty,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "Net Value",
            Name = DailyDataPointers.NetValue,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "New Outlets",
            Name = DailyDataPointers.NewOutletsCreated,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "No Of Other Activities",
            Name = DailyDataPointers.NoOfOtherActivities,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Fixed)
        {
            DisplayName = "MTDValue",
            Name = DailyDataPointers.MTDValue,
            IsForDashboard = false,
            IsForUser = true,
            IsForManager = true
        },
        //new ManagerAppPointers() { DisplayName = "MTDUnit",Name = DailyDataPointers.MTD unit", IsForUser = true, IsForManager = true, PointerType = PointerType.Fixed},
        new(PointerState.Other)
        {
            DisplayName = "Physical TC",
            Name = DailyDataPointers.PhycialCalls,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "New Outlet Value",
            Name = DailyDataPointers.NewOutletSalesInRevenue,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Other)
        {
            DisplayName = "Telephonic Order Value",
            Name = DailyDataPointers.TelephonicOrders,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "Selected Journey Outlet",
            Name = DailyDataPointers.SelectedJourneyOutlet,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "Qty (Super Unit)",
            Name = DailyDataPointers.OrderInSuperUnits,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW PC",
            Name = DailyDataPointers.JWPC,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW OVC",
            Name = DailyDataPointers.JWOVC,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW OVT",
            Name = DailyDataPointers.JWOVT,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW Sales (Unit)",
            Name = DailyDataPointers.JWSalesUnit,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW Sales (Std. Unit)",
            Name = DailyDataPointers.JWSalesStdUnit,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "JW Sales Value",
            Name = DailyDataPointers.JWSalesValue,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "LPC",
            Name = DailyDataPointers.LPC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Other)
        {
            DisplayName = "ULC",
            Name = DailyDataPointers.ULC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = false
        },
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "UPC",
            Name = DailyDataPointers.UPC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "UTC",
            Name = DailyDataPointers.UTC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Other, PointerState.Other)
        {
            DisplayName = "Journey diverted days",
            Name = DailyDataPointers.JourneyDivertedDays,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "Fill rate%",
            Name = DailyDataPointers.FillratePer,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true,
            useCompanySettings = "CompanyUsesOpenMarketOperations"
        },
        new(PointerState.Default, PointerState.Default)
        {
            DisplayName = "Dispatched calls (DC)",
            Name = DailyDataPointers.DC,
            IsForDashboard = true,
            IsForUser = true,
            IsForManager = true,
            useCompanySettings = "CompanyUsesOpenMarketOperations"
        },
        new(PointerState.Other)
        {
            DisplayName = "PW Scheduled Visits",
            Name = DailyDataPointers.PWScheduledVisits,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Total PW Visits",
            Name = DailyDataPointers.TotalPWVisits,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Primary Working Productive Visits",
            Name = DailyDataPointers.TotalPWProductiveVisits,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Avg PLPC",
            Name = DailyDataPointers.AvgPLPC,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Primary Working OVC %",
            Name = DailyDataPointers.PWOVCPercentage,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Average Time Spent",
            Name = DailyDataPointers.PWAvgTimeSpent,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Total Primary Working JW Sessions",
            Name = DailyDataPointers.PWJWSessions,
            IsForDashboard = false,
            IsForUser = false,
            IsForManager = true
        },
        new(PointerState.Other)
        {
            DisplayName = "Log out time",
            Name = DailyDataPointers.LogOutTime,
            IsForDashboard = true,
            IsForUser = false,
            IsForManager = false
        },
        new(PointerState.Other)
        {
            DisplayName = "CAP adherance %",
            Name = DailyDataPointers.CAPAdherancePer,
            IsForDashboard = true,
            IsForUser = false,
            IsForManager = false
        }
    };

    // Used in New Dashboard Daily summary, Manager Daily Data
    public static List<ManagerAppPointers> Pointers =>
        new()
        {
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Total Users",
                Name = DailyDataPointers.TotalUsers,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Master,
                ManagerOrder = 1
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Retailing",
                Name = DailyDataPointers.Retailing,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 2
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Official Work",
                Name = DailyDataPointers.OfficialWork,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 3
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Leave",
                Name = DailyDataPointers.Leave,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 4
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Planned Leave",
                Name = DailyDataPointers.PlannedLeave,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 100
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Absent",
                Name = DailyDataPointers.Absent,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 5,
                UserOrder = 1
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Reporting Manager",
                Name = DailyDataPointers.ReportingManager,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Master,
                ManagerOrder = 101,
                UserOrder = 100
            },
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Field User",
                Name = DailyDataPointers.FieldUserName,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Master,
                ManagerOrder = 102,
                UserOrder = 101
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Field User HQ",
                Name = DailyDataPointers.FieldUserHQ,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Master,
                ManagerOrder = 103,
                UserOrder = 102
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Rank",
                Name = DailyDataPointers.FieldUserRole,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Master,
                ManagerOrder = 104,
                UserOrder = 102
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Assigned Type",
                Name = DailyDataPointers.AssignedReasonCategory,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 103
            },
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Type",
                Name = DailyDataPointers.ReasonCategory,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 104
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Assigned Reason",
                Name = DailyDataPointers.AssignedReason,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 105
            },
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Reason",
                Name = DailyDataPointers.Reason,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 106
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Assigned JW User",
                Name = DailyDataPointers.AssignedJointWorkingEmployee,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 107
            },
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Selected JW User",
                Name = DailyDataPointers.JointWorkingEmployee,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 108
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Assigned Beat",
                Name = DailyDataPointers.AssignedBeat,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 109
            },
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Selected Beat",
                Name = DailyDataPointers.SelectedBeat,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 110
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Assigned Route",
                Name = DailyDataPointers.AssignedRoute,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 111
            },
            new ManagerAppPointers(PointerState.Fixed, pointerStateForDashboard: PointerState.Fixed)
            {
                DisplayName = "Selected Route",
                Name = DailyDataPointers.SelectedRoute,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 112
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Distributor",
                Name = DailyDataPointers.Distributor,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 113
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "Day Start Location",
                Name = DailyDataPointers.DayStartLocation,
                IsForDashboard = false,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Fact,
                UserOrder = 114
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "Login",
                Name = DailyDataPointers.Login,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Measure,
                UserOrder = 1
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "First Call",
                Name = DailyDataPointers.FirstCallTime,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Measure,
                UserOrder = 2
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "First PC",
                Name = DailyDataPointers.FirstPCTime,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                ColumnType = ColumnType.Measure,
                UserOrder = 3
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "SC",
                Name = DailyDataPointers.SC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 6,
                UserOrder = 4
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "TC",
                Name = DailyDataPointers.TC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 7,
                UserOrder = 6
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "CAP",
                Name = DailyDataPointers.CAP,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 8,
                UserOrder = 5
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "PC",
                Name = DailyDataPointers.PC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 9,
                UserOrder = 7
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "Productivity(%)",
                Name = DailyDataPointers.Productivity,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 10,
                UserOrder = 8
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "Sch.Productivity(%)",
                Name = DailyDataPointers.SchProductivity,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 11,
                UserOrder = 9
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "OVT (%)",
                Name = DailyDataPointers.OVT,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 15,
                UserOrder = 13
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "OVC (%)",
                Name = DailyDataPointers.OVC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 14,
                UserOrder = 12
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "TO",
                Name = DailyDataPointers.TelephonicOrders,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 19,
                UserOrder = 18
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "JW Calls",
                Name = DailyDataPointers.JointWorkingCalls,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 17,
                UserOrder = 16
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "Qty (Std Unit)",
                Name = DailyDataPointers.OrderInStdUnits,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 13,
                UserOrder = 11
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "FOC",
                Name = DailyDataPointers.TotalSchemeQty,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 20,
                UserOrder = 19
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "Net Value",
                Name = DailyDataPointers.NetValue,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 12,
                UserOrder = 10
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "Net Value With Tax",
                Name = DailyDataPointers.NetValueWithTax,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 18,
                UserOrder = 17
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "New Outlets",
                Name = DailyDataPointers.NewOutletsCreated,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 15,
                UserOrder = 14
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "No Of Other Activities",
                Name = DailyDataPointers.NoOfOtherActivities,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 16,
                UserOrder = 15
            },
            new ManagerAppPointers(PointerState.Fixed)
            {
                DisplayName = "MTDValue",
                Name = DailyDataPointers.MTDValue,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 105,
                UserOrder = 1
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Physical TC",
                Name = DailyDataPointers.PhycialCalls,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 40,
                UserOrder = 18
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "New Outlet Value",
                Name = DailyDataPointers.NewOutletSalesInRevenue,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 106
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Selected Journey Outlet",
                Name = DailyDataPointers.SelectedJourneyOutlet,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 17,
                UserOrder = 16
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "Qty (Super Unit)",
                Name = DailyDataPointers.OrderInSuperUnits,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 41,
                UserOrder = 43
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "UPC",
                Name = DailyDataPointers.UPC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 48,
                UserOrder = 115
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "UTC",
                Name = DailyDataPointers.UTC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 49,
                UserOrder = 116
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Other)
            {
                DisplayName = "Journey diverted days",
                Name = DailyDataPointers.JourneyDivertedDays,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 50,
                UserOrder = 117
            },
            new ManagerAppPointers(PointerState.Default)
            {
                DisplayName = "Telephonic Order Value",
                Name = DailyDataPointers.TelephonicOrdersValue,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "LPC",
                Name = DailyDataPointers.LPC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                UserOrder = 118
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "ULC",
                Name = DailyDataPointers.ULC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = false,
                UserOrder = 119
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Default)
            {
                DisplayName = "Fill rate%",
                Name = DailyDataPointers.FillratePer,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 51,
                UserOrder = 120,
                useCompanySettings = "CompanyUsesOpenMarketOperations"
            },
            new ManagerAppPointers(PointerState.Default, PointerState.Default)
            {
                DisplayName = "Dispatched calls (DC)",
                Name = DailyDataPointers.DC,
                IsForDashboard = true,
                IsForUser = true,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 52,
                UserOrder = 121,
                useCompanySettings = "CompanyUsesOpenMarketOperations"
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "PW Scheduled Visits",
                Name = DailyDataPointers.PWScheduledVisits,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 110
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Total PW Visits",
                Name = DailyDataPointers.TotalPWVisits,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 111
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Primary Working Productive Visits",
                Name = DailyDataPointers.TotalPWProductiveVisits,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 112
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Avg PLPC",
                Name = DailyDataPointers.AvgPLPC,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 113
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Primary Working OVC %",
                Name = DailyDataPointers.PWOVCPercentage,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 114
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Average Time Spent",
                Name = DailyDataPointers.PWAvgTimeSpent,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 115
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Total Primary Working JW Sessions",
                Name = DailyDataPointers.PWJWSessions,
                IsForDashboard = false,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 116
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "Log out time",
                Name = DailyDataPointers.LogOutTime,
                IsForDashboard = true,
                IsForUser = false,
                IsForManager = false,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 117
            },
            new ManagerAppPointers(PointerState.Other)
            {
                DisplayName = "CAP adherance %",
                Name = DailyDataPointers.CAPAdherancePer,
                IsForDashboard = true,
                IsForUser = false,
                IsForManager = true,
                ColumnType = ColumnType.Measure,
                ManagerOrder = 118
            }
        };
}

public class ManagerAppPointers
{
    public ManagerAppPointers(PointerState pointerStateForUsers,
        PointerState pointerStateForManagers = PointerState.Unknown, PointerState pointerStateForDashboard = PointerState.Unknown)
    {
        PointerStateForUsers = pointerStateForUsers;
        PointerStateForManagers = pointerStateForManagers == PointerState.Unknown
            ? pointerStateForUsers
            : pointerStateForManagers;
        PointerStateForDashboard = pointerStateForDashboard;
    }

    public ColumnType ColumnType { get; set; }
    public string DisplayName { get; set; }
    public bool IsForManager { get; set; }
    public bool IsForUser { get; set; }
    public bool IsForDashboard { get; set; }
    public int ManagerOrder { get; set; }
    public DailyDataPointers Name { get; set; }
    public PointerState PointerStateForManagers { get; set; }
    public PointerState PointerStateForUsers { get; set; }
    public PointerState PointerStateForDashboard { get; set; }
    public int UserOrder { get; set; }
    public string useCompanySettings { get; set; }
}
