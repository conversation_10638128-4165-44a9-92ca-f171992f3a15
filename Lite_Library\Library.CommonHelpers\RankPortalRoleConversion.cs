﻿using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public static class RankPortalRoleConversion
{
    public static PortalUserRole GetPortalRoleFromRank(EmployeeRank rank)
    {
        switch (rank)
        {
            case EmployeeRank.ESM:
                return PortalUserRole.ClientEmployee;

            case EmployeeRank.ASM:
                return PortalUserRole.AreaSalesManager;

            case EmployeeRank.RSM:
                return PortalUserRole.RegionalSalesManager;

            case EmployeeRank.ZSM:
                return PortalUserRole.ZonalSalesManager;

            case EmployeeRank.NSM:
                return PortalUserRole.NationalSalesManager;

            case EmployeeRank.GSM:
                return PortalUserRole.GlobalSalesManager;

            default:
                return PortalUserRole.GlobalAdmin;
        }
    }
}
