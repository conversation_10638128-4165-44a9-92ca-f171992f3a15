﻿using System;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Storage.Queues.Models;

namespace Library.Infrastructure.QueueService;

public class QueueHandlerService
{
    private readonly string _connectionString;

    public QueueHandlerService(string connectionString)
    {
        _connectionString = connectionString;
    }

    public async Task AddToQueue<T>(string queueString, T data, TimeSpan? visibilityTimeout = null,
        TimeSpan? timeToLive = null)
    {
        var queueAction = new QueueActions(queueString, _connectionString);
        await queueAction
            .AddToQueue(JsonSerializer.Serialize(data), visibilityTimeout, timeToLive)
            .ConfigureAwait(false);
    }

    public async Task DeleteMessage(string queueString, QueueMessage[] retrievedMessage)
    {
        var queueAction = new QueueActions(queueString, _connectionString);
        await queueAction.DeleteMessage(retrievedMessage).ConfigureAwait(false);
    }

    public async Task<QueueMessage> GetItem(string queueString)
    {
        var queueAction = new QueueActions(queueString, _connectionString);
        return await queueAction.GetItem().ConfigureAwait(false);
    }

    public async Task<int> GetMessageCount(string queueName)
    {
        var queueAction = new QueueActions(queueName, _connectionString);
        return await queueAction.GetMessageCount().ConfigureAwait(false);
    }
}
