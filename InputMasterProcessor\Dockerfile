FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
WORKDIR /app


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["InputMasterProcessor/InputMasterProcessor.csproj", "InputMasterProcessor/"]
COPY ["Lite_Library/Library.ConnectionStringParsor/Library.ConnectionStringParsor.csproj", "Lite_Library/Library.ConnectionStringParsor/"]
COPY ["Lite_Library/Library.StorageWriter/Library.StorageWriter.csproj", "Lite_Library/Library.StorageWriter/"]
COPY ["Lite_Library/Libraries.Cryptography/Libraries.Cryptography.csproj", "Lite_Library/Libraries.Cryptography/"]
COPY ["RouteOptimizationProcessor.Core/RouteOptimizationProcessor.Core.csproj", "RouteOptimizationProcessor.Core/"]
COPY ["Lite_Library/EntityHelper/EntityHelper.csproj", "Lite_Library/EntityHelper/"]
COPY ["Lite_Library/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "Lite_Library/Libraries.CommonEnums/"]
COPY ["Lite_Library/Libraries.CommonModels/Libraries.CommonModels.csproj", "Lite_Library/Libraries.CommonModels/"]
COPY ["Lite_Library/Library.NumberSystem/Library.NumberSystem.csproj", "Lite_Library/Library.NumberSystem/"]
COPY ["Lite_Library/Library.Infrastructure/Library.Infrastructure.csproj", "Lite_Library/Library.Infrastructure/"]
COPY ["RouteOptimizationProcessor.DbStorage/RouteOptimizationProcessor.DbStorage.csproj", "RouteOptimizationProcessor.DbStorage/"]
RUN dotnet restore "./InputMasterProcessor/InputMasterProcessor.csproj"
COPY . .
WORKDIR "/src/InputMasterProcessor"
RUN dotnet build "./InputMasterProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./InputMasterProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "InputMasterProcessor.dll"]