﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum ConstraintType
{
    None = 0,
    Amount = 1,

    [Display(Name = "Quantity (Unit)")]
    Quantity = 2,

    [Display(Name = "Standard Unit")]
    Standard_Unit = 3,

    [Display(Name = "Super Unit")]
    Super_Unit = 4
}

public enum DiscountBlock
{
    Primary_Category = 1, //1.	PC or combination of PCs
    Secondary_Category = 2, //2.	SC or combination of SCs with in any one PC.
    Product = 3, //3.	Product/SKU or combination of Products /SKU with in any one SC.
    All = 4,
    Master_Batch = 5,
    SchemeBasket = 6,
    Product_Division = 7,
    Attribute_Text_1 = 8,
    PPG = 9
}

public enum PayoutCalculationType
{
    None = 0,
    Step = 1,
    Continuous = 2,
    ProRata = 3,
    Recurring = 4
}

public enum PayoutType
{
    FOC = 1,
    Article = 2,
    Discount = 3,
    Product = 4,
    TopUpDiscount = 5,
    PerUnitDiscount = 6,
    Basket = 7,
    FixedValueDiscount = 8,
    BasketTypePerUnitDiscount = 9,
    Coupon = 10,
    PercentageDiscountOnMRP = 11,
    NetLandingPrice = 12
}

public enum QualifierPayoutType
{
    [Display(Name = "Quantity (Unit)")]
    Quantity = 0,

    [Display(Name = "Standard Unit")]
    Standard_Unit = 1
}

public enum SchemeCategorization
{
    Uncategorized = 0,
    Primary = 1,
    Secondary = 2,
    Tertiary = 3
}

public enum SchemeSubType
{
    Extendable = 1,
    NonExtendable = 2,
    Claimable = 3,
    NonClaimable = 4,
    NonExtendableCompanytoSSAndStockistOnly = 5
}

public enum SchemeType
{
    Primary = 1,
    Secondary = 2
}

public enum PayoutIn
{
    [Display(Name = "Amount")]
    Amount = 1,

    [Display(Name = "Quantity (Unit)")]
    Quantity = 2,

    [Display(Name = "Standard Unit")]
    Standard_Unit = 3
}

public enum BudgetType
{
    [Display(Name = "Amount")]
    Amount = 0,

    [Display(Name = "Quantity (Unit)")]
    Quantity = 1,

    [Display(Name = "Standard Unit")]
    Standard_Unit = 2,

    [Display(Name = "Super Unit")]
    Super_Unit = 3
}

public enum SchemeApplicableAtLevel
{
    Company = 0,
    Zone = 1,
    Region = 2,
    Distributor = 3,
    Territory = 4,
    Beat = 5
}

//Specific to Coke
public enum Calculation_Method
{
    [Display(Name = "Free Bottle")]
    Free_Bottle = 4,

    [Display(Name = "Amount Per Case")]
    Amount_Per_Case = 1,

    [Display(Name = "Percentage Per Case")]
    Percentage_Per_Case = 3,

    [Display(Name = "Percentage of MRP")]
    Percentage_of_MRP = 5,

    [Display(Name = "Net Landing Price")]
    Net_Landing_Price = 6
}

//Specific to Coke
public enum SchemeDisbursementMethods
{
    Spot = 1, //Applibable on outlets with OMS enable tag.
    PeriodicRestrictive = 2, // Spot is not applicable on products on which this is active on the outlet
    PeriodicNonRestrictive = 3, // Not Applicable for FA. We can reject
    ConsumerPromo = 4 // Always applicable on top of other schemes
}

//Specific to Coke
public enum MarketScope
{
    OutletScheme = 1,
    DistributorScheme = 3,
    OutletChannel = 6,
    ShopType = 8
}

//Specific to Coke
public enum MonitoringScope
{
    [Display(Name = "IPC")]
    AlternateCategory = 1,
    SKULevel = 2,
    BundleScheme = 3
}

public enum DiscountType
{
    Unknown = 0,
    Cash_Discount = 1, // Can Avail bill Amount reduction equal to the Cash Discount
    FOC = 2, //Allows to use the Discount Amount only for Purchase in smae Block
    Article = 3,
    Open_FOC = 4,
    Other_Discount = 100
}

public enum CappingType
{
    [Display(Name = "Amount")]
    Amount = 1,

    [Display(Name = "Quantity (Unit)")]
    Quantity = 2,

    [Display(Name = "Quantity (Standard Unit)")]
    Standard_Unit = 3,

    [Display(Name = "Quantity (Super Unit)")]
    Super_Unit = 4
}

public enum AdditionalCalculationLogic
{
    Discount_On_Mrp = 0,
    Discount_On_Ptr = 1
}
