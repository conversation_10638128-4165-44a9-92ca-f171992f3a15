﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class DayStartPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public DayStartPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
    {
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "GSM",
            Name = "GSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.Employee,
            Name = "ESM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Reporting Manager",
            Name = "ReportingManager",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Name",
            Name = "FieldUserName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Field User ERP ID",
            Name = "FieldUserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "User Designation",
            Name = "UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },

        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position,
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position,
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position,
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position,
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position,
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position,
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position,
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position,
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Reporting Manager",
            Name = "L2Position_ReportingUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser Name",
            Name = "L1Position_FieldUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Field User ERP ID",
            Name = "L1Position_UserERP",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "User Designation",
            Name = "L1Position_UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText1,
            Name = "EmployeeAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText2,
            Name = "EmployeeAttributeText2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber1,
            Name = "EmployeeAttributeNumber1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber2,
            Name = "EmployeeAttributeNumber2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Date Of Joining",
            Name = "DateOfJoining",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Email Id",
            Name = "EmailId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "User Type",
            Name = "UserType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Contact Number",
            Name = "ContactNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Is App Used?",
            Name = "DayStart",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Count,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Assigned Type",
            Name = "AssignedReasonCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Type",
            Name = "ReasonCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Assigned Reason",
            Name = "AssignedReason",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Reason",
            Name = "Reason",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Assigned JW User",
            Name = "AssignedJointWorkingEmployee",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Selected JW User",
            Name = "JointWorkingEmployee",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Assigned" + linkNames.Beat,
            Name = "AssignedBeat",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Selected" + linkNames.Beat,
            Name = "SelectedBeat",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = linkNames.Distributor,
            Name = "Distributor",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = linkNames.Distributor + " Erp Id",
            Name = "DistributorErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = linkNames.Route,
            Name = "Route",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = linkNames.Route + " Erp Id",
            Name = "RouteErpId",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Log In",
            Name = "Login",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Log Out",
            Name = "Logout",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Is Late",
            Name = "IsLate",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Day End",
            Name = "IsNormallyEnd",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Total time",
            Name = "TotalTime",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "First Call",
            Name = "FirstCallTime",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "First Call OVC?",
            Name = "IsFirstCallOVC",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Last Call",
            Name = "LastCallTime",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Total Retailng Time",
            Name = "TotalRetailTime",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Total Retailng Time (Physical Calls)",
            Name = "TotalRetailTimePhysical",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "First PC",
            Name = "FirstPCTime",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Last PC",
            Name = "LastPCTime",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "TC",
            Name = "TC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. TC",
            Name = "AvgTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "PC",
            Name = "PC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. PC",
            Name = "AvgPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "PC (Sch.)",
            Name = "SPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "OVT",
            Name = "OVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. OVT",
            Name = "AvgOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "OVT (%)",
            Name = "PerOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Percent
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "OVC",
            Name = "OVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. OVC",
            Name = "AvgOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "OVC (%)",
            Name = "PerOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Percent
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "SC",
            Name = "SC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "TO",
            Name = "TelephonicOrders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. TO",
            Name = "AvgTelephonicOrders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Productivity",
            Name = "Productivity",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Productivity (Sch.)",
            Name = "ScheduledProductivity",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Effective Calls",
            Name = "EffectiveCalls",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. Effective Calls",
            Name = "AvgEffectiveCalls",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Scheme Effective Calls",
            Name = "SchemeEffectiveCalls",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Avg. Scheme Effective Calls",
            Name = "AvgSchemeEffectiveCalls",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = linkNames.Employee + " JW Calls",
            Name = "JointWorkingCalls",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Manager JW Calls",
            Name = "ManagerJointWorkingCalls",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "New " + linkNames.Outlets,
            Name = "NewOutletsCreated",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "New " + linkNames.Outlets + " (Net Value)",
            Name = "NewOutletSalesInRevenue",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "New " + linkNames.Outlets + $" (Qty in {linkNames.StdUnit})",
            Name = "NewOutletSalesInStdUnits",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "New " + linkNames.Outlets + $" (Qty in {linkNames.Unit})",
            Name = "NewOutletSalesInUnits",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "New " + linkNames.Outlets + $" (Qty in {linkNames.SuperUnit})",
            Name = "NewOutletSalesInSupUnits",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "No Of Other Activities",
            Name = "NoOfOtherActivities",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "First Other Activity Time",
            Name = "FirstOtherActivityTime",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Last Other Activity Time",
            Name = "LastOtherActivityTime",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Time Spent in Other Activities",
            Name = "TimeSpentinOtherActivities",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Visit",
            DisplayName = "Selected Journey " + linkNames.Outlets,
            Name = "SelectedJourneyOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Value",
            Name = "OrderInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg. Value",
            Name = "AvgOrderInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Qty ({linkNames.StdUnit})",
            Name = "OrderInStdUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Avg. Qty ({linkNames.StdUnit})",
            Name = "AvgOrderInStdUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Qty ({linkNames.Unit})",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Avg. Qty ({linkNames.Unit})",
            Name = "AvgOrderInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Avg. Qty ({linkNames.SuperUnit})",
            Name = "AvgOrderInSupUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Qty ({linkNames.SuperUnit})",
            Name = "OrderQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Discount",
            Name = "ProductWiseDiscount",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FOC",
            Name = "TotalSchemeQty",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Scheme Discount",
            Name = "TotalSchemeDiscount",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Net Value",
            Name = "NetValue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg. Net Value",
            Name = "AvgNetValue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Net Value (Sales)",
            Name = "DispatchInRevenue",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Sale Qty ({linkNames.SuperUnit})",
            Name = "DispatchInSupUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Sale Qty ({linkNames.StdUnit})",
            Name = "DispatchInStdUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Sale Qty ({linkNames.Unit})",
            Name = "DispatchInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LPC",
            Name = "LPC",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Styles per Call",
            Name = "StylePerCall",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Secondary Category Per Call",
            Name = "SecondaryCategoryPerCall",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "CAP",
            Name = "CAP",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Covered (%)",
            Name = "Covered",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "DayStartYear",
            Name = "Year",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Date",
            Name = "Date",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Week",
            Name = "Week",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Telephonic Validated Calls",
            Name = "TelephonicValidated",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Telephonic Validated Time",
            Name = "TotalValidatedTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.MustSell + " PC",
            Name = "MustSellPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.MustSell + " Order " + linkNames.Unit,
            Name = "MustSellOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.MustSell + " Order " + linkNames.StdUnit,
            Name = "MustSellOrderInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.MustSell + " Order " + linkNames.SuperUnit,
            Name = "MustSellOrderInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.MustSell + " Order Net Value",
            Name = "MustSellNetOrderInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = linkNames.MustSell + " " + linkNames.LPC,
            Name = "MustSellLPC",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        }
    };
}
