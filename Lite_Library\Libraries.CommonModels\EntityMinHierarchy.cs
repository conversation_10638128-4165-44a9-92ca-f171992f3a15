﻿using Libraries.CommonEnums;

namespace Libraries.CommonModels;

public class EntityMinUser
{
    public long UserId { get; set; }
    public long PositionCodeIds { get; set; }
    public PortalUserRole UserRole { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }
}

public class EntityMinWithParent : EntityMin
{
    public EntityMinWithParent Parent { get; set; }
}

public class EntityUserMin : EntityMin
{
    public PortalUserRole UserRole { get; set; }
    public string PhoneNumber { get; set; }
}

//TODO: Delete below
public class BeatMinWithParent : EntityMin
{
    public string BeatErpId { get; set; }
    public BeatMinWithParent Parent { get; set; }
}

public class EntityUserMinForEngage
{
    public long UserId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public long? PositionCodeId { get; set; }
}
