﻿using System;

namespace Library.ConnectionStringParsor;

public class CosmosConnectionString : ConnectionStringBase
{
    private CosmosConnectionString(string connectionString) : base(connectionString)
    {
        var parsedString = Parse();
        if (parsedString.TryGetValue("accountendpoint", out var value))
        {
            AccountEndpoint = value;
        }
        else
        {
            throw new Exception("MalFormed Connection String: 'accountendpoint' is compulsory");
        }

        if (parsedString.TryGetValue("accountkey", out var value1))
        {
            AccountKey = value1;
        }
        else
        {
            throw new Exception("MalFormed Connection String: 'accountkey' is compulsory");
        }
    }

    public string AccountEndpoint { get; }
    public string AccountKey { get; }

    public static CosmosConnectionString GetConnection(string connString)
    {
        return new CosmosConnectionString(connString);
    }
}
