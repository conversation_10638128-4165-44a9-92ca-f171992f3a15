﻿using Libraries.CommonEnums;

namespace RouteOptimizationProcessor.Core.Models.QueueModels;

public class RoutePlaygroundQueueModel
{
    public long Id { get; set; }
    public string FileName { get; set; }
    public int MaximumDailyVisits { get; set; }
    public int MaximumDailyDistance { get; set; }
    public string SpectralCoefficient { get; set; }
    public string JourneyType { get; set; }
    public string OutlierAddition { get; set; }
    public string Country { get; set; }
}

public class PlayGroundRouteUploadQueueModel
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public required string FilePath { get; set; } // complete file path to the uploaded file
}