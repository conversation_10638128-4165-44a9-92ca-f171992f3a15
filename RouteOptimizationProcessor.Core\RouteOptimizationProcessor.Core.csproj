﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Lite_Library\EntityHelper\EntityHelper.csproj" />
    <ProjectReference Include="..\Lite_Library\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Lite_Library\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.NumberSystem\Library.NumberSystem.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.StorageWriter\Library.StorageWriter.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.Infrastructure\Library.Infrastructure.csproj" />
  </ItemGroup>

</Project>
