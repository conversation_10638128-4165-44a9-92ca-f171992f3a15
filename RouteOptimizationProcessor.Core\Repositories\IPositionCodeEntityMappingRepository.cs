﻿using RouteOptimizationProcessor.Core.Models.CoreModels;


namespace RouteOptimizationProcessor.Core.Repositories;

public interface IPositionCodeEntityMappingRepository
{
    Task<List<PositionCodeEntityMappingMin>> GetAllPositionCodesUnderUserAsync(long companyId, List<long> positionCodeIds, bool isAdmin, bool includeUser);

    Task<List<PositionCodeEntityMappingMin>> GetPositionUsersAsync(long companyId, List<long> pcIds);
}
