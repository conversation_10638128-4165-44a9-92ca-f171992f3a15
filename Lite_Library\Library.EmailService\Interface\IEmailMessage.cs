﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Library.Infrastructure.Interface;
using Library.SlackService.Model;
using Library.StorageWriter.Reader_Writer;

namespace Library.EmailService.Interface;

public interface IEmailMessage : IQueueMessage
{
    string Bcc { get; set; }
    string Cc { get; set; }
    string FromEmail { get; set; }
    string FromName { get; set; }
    bool IsHTML { get; set; }
    string Message { get; set; }
    string Subject { get; set; }
    string To { get; set; }
}

public class EmailMessage : IEmailMessage
{
    public string ContentPath { get; set; }
    public string Bcc { get; set; }
    public string Cc { get; set; }
    public string FromEmail { get; set; }
    public string FromName { get; set; }
    public bool IsHTML { get; set; }
    public string Message { get; set; }
    public string Subject { get; set; }
    public string To { get; set; }

    internal async Task VerifyContent(string masterStorageConnectionString)
    {
        if ((Message?.Length ?? 0) > 45000)
        {
            var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);
            var filepath = $"{Guid.NewGuid()}.html";
            Message = await blobWriter.WriteToBlob(filepath, Message, "text/html").ConfigureAwait(false);
            ContentPath = Message;
        }
    }

    public ISlackMessage ToSlackMessage(string channel)
    {
        return new SlackMessage
        {
            Channel = channel,
            Text = $"Email:{Subject}",
            Username = "DebugEmailRequest",
            Attachments = new List<Attachment>
            {
                new()
                {
                    Title = "EmailDetails",
                    Fields = new List<Field>
                    {
                        new() { Title = "From", Value = $"[{FromName}]{FromEmail}", Short = true },
                        new() { Title = "To", Value = To, Short = true },
                        new() { Title = "Subject", Value = Subject, Short = false },
                        new() { Title = "BCC", Value = Bcc, Short = true },
                        new() { Title = "IsHtml", Value = IsHTML.ToString(), Short = true },
                        new() { Title = "Body", Value = Message, Short = false }
                    }
                }
            }
        };
    }
}
