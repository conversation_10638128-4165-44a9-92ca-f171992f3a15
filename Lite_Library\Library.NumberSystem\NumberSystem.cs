﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Library.NumberSystem;

public enum ColorValue
{
    Red = 1,
    Green = 2,
    Yellow = 3
}

public enum NumberSystems
{
    [Display(Name = "Indian")]
    Indian = 0,

    [Display(Name = "International")]
    International = 1
}

public class ChartColorCodingCommon
{
    public double Defaulters { get; set; }
    public string Measure { get; set; }
    public double Performers { get; set; }
}

public class FormattedData
{
    public FormattedData()
    {
    }

    public FormattedData(string stringVal, double value)
    {
        Value = value;
        StringVal = stringVal;
    }

    public FormattedData(string stringVal, double value, double defaulters, double performers)
    {
        Value = value;
        StringVal = stringVal;
        ColorValue = value <= defaulters ? ColorValue.Red :
            value >= performers ? ColorValue.Green : ColorValue.Yellow;
    }

    public FormattedData(double value)
    {
        Value = value;
    }

    public FormattedData(double value, double defaulters, double performers)
    {
        Value = value;
        ColorValue = value <= defaulters ? ColorValue.Red :
            value >= performers ? ColorValue.Green : ColorValue.Yellow;
    }

    public FormattedData(string stringVal)
    {
        StringVal = stringVal;
        Value = stringVal;
    }

    public string ColorName
    {
        get
        {
            switch (ColorValue)
            {
                case ColorValue.Red:
                    return "#facdcc";
                case ColorValue.Green:
                    return "#dbedd4";
                case ColorValue.Yellow:
                    return "#fef3da";
                default:
                    return "";
            }
        }
    }

    public ColorValue ColorValue { get; set; }
    public string StringVal { get; set; }
    public object Value { get; set; }
}

public class NumberSystem
{
    private readonly NumberSystems numberSystemType;

    public NumberSystem(NumberSystems numberSystemType)
    {
        this.numberSystemType = numberSystemType;
    }

    private FormattedData GetFormattedValueWithColor(double dataValue, ChartColorCodingCommon chartColorCoding)
    {
        var formattedValue = GetFormattedValue(dataValue);
        formattedValue.ColorValue = dataValue <= chartColorCoding.Defaulters ? ColorValue.Red :
            dataValue >= chartColorCoding.Performers ? ColorValue.Green : ColorValue.Yellow;
        return formattedValue;
    }

    public FormattedData FormatData(object val, string measure = null)
    {
        var isDouble = double.TryParse(val.ToString(), out var dataValue);
        var isTime = TimeSpan.TryParse(val.ToString(), out var dataTime);
        if (isDouble)
        {
            if (measure != null && measure.ToLower().Contains("time") && !measure.ToLower().Contains("_"))
            {
                var timeSpan = TimeSpan.FromMinutes(dataValue);
                return new FormattedData(
                    timeSpan.Hours + ":" + timeSpan.Minutes + ":" + timeSpan.Seconds, dataValue);
            }

            if (measure != null && measure.ToLower().Contains("mobilenumber"))
            {
                return new FormattedData(val.ToString());
            }

            return GetFormattedValue(dataValue);
        }

        if (isTime)
        {
            return new FormattedData(val.ToString(), dataTime.TotalMinutes);
        }

        return new FormattedData(val.ToString());
    }

    public FormattedData FormatDataWithColorCoding(object val, ChartColorCodingCommon chartColorCoding,
        string measure = null)
    {
        var isDouble = double.TryParse(val.ToString(), out var dataValue);
        var isTime = TimeSpan.TryParse(val.ToString(), out var dataTime);
        if (isDouble)
        {
            if (measure != null && measure.ToLower().Contains("time"))
            {
                var timeSpan = TimeSpan.FromMinutes(dataValue);
                return new FormattedData(
                    Math.Floor(timeSpan.TotalHours) + ":" + timeSpan.Minutes + ":" + timeSpan.Seconds, dataValue,
                    chartColorCoding.Defaulters, chartColorCoding.Performers);
            }

            return GetFormattedValueWithColor(dataValue, chartColorCoding);
        }

        if (isTime)
        {
            return new FormattedData(val.ToString(), dataTime.TotalMinutes, chartColorCoding.Defaulters,
                chartColorCoding.Performers);
        }

        return new FormattedData(val.ToString());
    }

    public FormattedData GetFormattedValue(double val, bool usesSuperUnitMT = false, bool toKm = false)
    {
        if (usesSuperUnitMT)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000)
            {
                var div = Math.Round(val / 1000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.00")) + " MT";
            }
            else if (val < 1000)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }

        if (toKm)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000)
            {
                var div = Math.Round(val / 1000, 2);
                stringVal = div.ToString("0.##") + " km";
                formattedData.StringVal = stringVal;
                return formattedData;
            }

            stringVal = val.ToString("0.##") + " m";
            formattedData.StringVal = stringVal;
            return formattedData;
        }

        if (numberSystemType == NumberSystems.International)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000000000)
            {
                var div = Math.Round(val / 1000000000.0, 1);
                stringVal = (div == (int)div ? ((int)div).ToString("##,###") : div.ToString("##,###.0")) + " B";
            }
            else if (val >= 1000000)
            {
                var div = Math.Round(val / 1000000.0, 1);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.0")) + " M";
            }
            else if (val >= 1000)
            {
                var div = Math.Round(val, 1);
                stringVal = div == (int)div ? ((int)div).ToString("###,###") : div.ToString("###,###.#");
            }
            else if (val < 1)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }
            else if (val < 1000)
            {
                var div = Math.Round(val, 1);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
        else
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 10000000000)
            {
                var div = Math.Round(val / 10000000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString("##,###") : div.ToString("##,###.0")) + " Cr.";
            }
            else if (val >= 10000000)
            {
                var div = Math.Round(val / 10000000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.00")) + " Cr.";
            }
            else if (val >= 100000)
            {
                var div = Math.Round(val / 100000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.00")) + " Lac";
            }
            else if (val >= 1000)
            {
                var div = val;
                stringVal = div == (int)div ? ((int)div).ToString("##,###") : div.ToString("##,###.#");
            }
            else if (val < 1)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }
            else if (val < 1000)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
    }
}
