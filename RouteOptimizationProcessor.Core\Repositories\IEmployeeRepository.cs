﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace RouteOptimizationProcessor.Core.Repositories;

public interface IEmployeeRepository
{
    Task<List<EmployeeMinWithRank>> GetFieldUserIdsUnderManagerModelAsync(long companyId, PortalUserRole userRole, List<long> userIds,
        EmployeeType? userType = null);

    Task<List<EmployeeMinWithType>> GetAllEmployeesAsync(long companyId);

    Task<List<EmployeeMinWithType>> GetAllEmployeesSRAndDSRAsync(long companyId);

    Task<Dictionary<long, string>> GetEmployeeErpIdDictionaryAsync(long companyId, List<long> employeeIds);

    Task<(List<long>, List<long>, List<PositionCodeLevel>)> GetPositionCodesOfAndUnderUserAsync(long companyId, long employeeId);

    Task<List<EntityMinWithErp>> GetBeatsForPositionsAsync(long companyId, List<long> positionIds);

    Task<List<EntityMinWithErp>> GetBeatsForEmployeesAsync(long employeeId, long companyId);

    Task<List<EntityMinWithErp>> GetRoutesForEmployeeAsync(long companyId, long employeeId);

    Task<List<EntityMinWithErp>> GetRoutesForEmployeeViaDirectMappingAsync(long companyId, long employeeId);

    Task<List<EntityMinWithErp>> GetRoutesForPositions(long companyId, List<long> positionIds);

    Task<Dictionary<string, long>> GetActiveEmployeeErpDictionary(long companyId, List<string> empErpIds, CancellationToken ct);
}