using Libraries.CommonEnums;
using Library.StorageWriter.Reader_Writer;
using OfficeOpenXml;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Models.QueueModels;
using RouteOptimizationProcessor.Core.Repositories;

namespace RouteOptimizationProcessor.Core.Services;

public interface IUploadService
{
    Task UploadRoutes(PlayGroundRouteUploadQueueModel queueData, CancellationToken ct);
}

public class UploadService(ICompanySettingsRepository companySettingsRepository,
    IRoutePlanAutomationRepository routePlanAutomationRepository, 
    FaiDataLakeBlobWriter faiDataLakeBlobWriter, 
    IEmployeeRepository employeeRepository, 
    ILocationRepository locationRepository,
    IRoutePlanRepository routePlanRepository) : IUploadService
{

    private readonly List<string> possibleWeekOffStrings = ["Week Off", "Week off", "Weekoff", "WeekOff", "week off", "weekoff", "00Week off", "00Week Off"];
    private readonly DateTime currentDate = DateTime.UtcNow.Date;

    public async Task UploadRoutes(PlayGroundRouteUploadQueueModel queueData, CancellationToken ct)
    {
        var routePlanAutomation = await routePlanAutomationRepository.GetSingleAsync(queueData.Id, ct);
        if (routePlanAutomation == null)
        {
            throw new Exception($"Route plan automation with ID {queueData.Id} not found.");
        }

        // load excel file from blob storage
        var filePath = queueData.FilePath; 
        if (string.IsNullOrEmpty(filePath))
        {
            throw new Exception("File path is empty or null.");
        }
        var records = await LoadExcelDataAsync(queueData, ct);

        // get all the employee erp ids from the records
        var employeeErpIds = records.Select(r => r.EmployeeErpId).Distinct().ToList();
        if (employeeErpIds.Count == 0)
        {
            throw new Exception("No employee ERP IDs found in the records.");
        }
        // get employee erpid and employee id mapping from masters db
        var employeeIdMapping = await employeeRepository.GetActiveEmployeeErpDictionary(queueData.CompanyId, employeeErpIds, CancellationToken.None);
        // get outlet ErpIds from the records
        var outletErpIds = records.Select(r => r.OutletErpId).Distinct().ToList();
        if (outletErpIds.Count == 0)
        {
            throw new Exception("No outlet ERP IDs found in the records.");
        }
        // get outlet erp id and outlet id mapping from masters db
        var outletIdMapping = await locationRepository.GetLocationErpDictionary(queueData.CompanyId, outletErpIds, CancellationToken.None);

        if (employeeIdMapping.Count == 0 || outletIdMapping.Count == 0)
        {
            throw new Exception("No valid employee or outlet mappings found in the masters database.");
        }

        // map records to route plan automation model
        var routePlanAutomationRecords = records.Where(r => !possibleWeekOffStrings.Contains(r.OutletErpId)).Select(record =>
        {
            var employeeId = employeeIdMapping.TryGetValue(record.EmployeeErpId, out long value) ? value : 0;
            var outletId = outletIdMapping.TryGetValue(record.OutletErpId, out long outletValue) ? outletValue : 0;
            return new AutomaticDJPDbModel
            {
                Day = record.Day,
                EmployeeId = employeeId,
                OutletId = outletId,
                Sequence = record.Sequence
            };
        }).ToList();

        var invalidRecords = routePlanAutomationRecords.Where(r => r.EmployeeId == 0 || r.OutletId == 0).ToList();

        if (invalidRecords.Count > 0)
        {
            throw new Exception($"Invalid records found: {string.Join(", ", invalidRecords.Select(r => $"Day: {r.Day}, EmployeeId: {r.EmployeeId}, OutletId: {r.OutletId}"))}");
        }

        // add week off records to route plan automation model
        var weekOffRecords = records.Where(r => possibleWeekOffStrings.Contains(r.OutletErpId)).ToList();
        var weekOffAutomationRecords = weekOffRecords.Select(record =>
        {
            var employeeId = employeeIdMapping.TryGetValue(record.EmployeeErpId, out long value) ? value : 0;
            return new AutomaticDJPDbModel
            {
                Day = record.Day,
                EmployeeId = employeeId,
                OutletId = 0, // No outlet for week off
                Sequence = record.Sequence
            };
        }).ToList();

        // combine both records
        routePlanAutomationRecords.AddRange(weekOffAutomationRecords);

        var journeyVersion = await companySettingsRepository.GetJourneyPlanVersionForCompany(queueData.CompanyId);
        var usesPositionCodes = await companySettingsRepository.UsesPositionCodes(queueData.CompanyId);
        if (journeyVersion == JourneyPlanVersion.NewJourneyPlan)
        {
            // NJP
            var journeyEntity = await companySettingsRepository.GetNewJourneyPlanEntityForCompany(queueData.CompanyId);
            if (journeyEntity == JourneyPlanningEntity.Route)
            {
                // Route Plan
                // Implement logic for uploading routes in New Journey Plan Route Plan entity
                if (usesPositionCodes)
                {
                    // Handle position codes logic

                }
                else
                {
                    // Handle non-position codes logic
                }

            }
        }
        else
        {
            // OJP
            var journeyType = await companySettingsRepository.GetJourneyPlanTypeForCompany(queueData.CompanyId);
            if (journeyType == JourneyPlanType.RoutePlan)
            {
                // Route Plan
                // Implement logic for uploading routes in Old Journey Plan Route Plan type
                // No route Position Mapping existsng in OJP Route Plan
                await routePlanRepository.SaveRoutePlan(routePlanAutomationRecords, queueData.CompanyId, currentDate, currentDate, possibleWeekOffStrings, ct);
            }
        }
    }

    private async Task<List<AutomaticDJPExcelModel>> LoadExcelDataAsync(
        PlayGroundRouteUploadQueueModel queueData,
        CancellationToken ct = default)
    {
        var records = new List<AutomaticDJPExcelModel>();

        // Download file
        using var stream = await faiDataLakeBlobWriter.DownloadFileAsStreamAsync("ro-playground", queueData.FilePath, ct);
        using var package = new ExcelPackage(stream);
        var worksheet = package.Workbook.Worksheets[0];

        // Get column count & row count
        var rowCount = worksheet.Dimension?.Rows ?? 0;
        var colCount = worksheet.Dimension?.Columns ?? 0;

        // Build column index map
        var columnIndices = new Dictionary<string, int>();
        for (int col = 1; col <= colCount; col++)
        {
            string columnName = worksheet.Cells[1, col].Text;
            columnIndices[columnName] = col;
        }

        // Read data rows
        for (int row = 2; row <= rowCount; row++)
        {
            var record = new AutomaticDJPExcelModel
            {
                Day = worksheet.Cells[row, columnIndices["Day"]].GetValue<int>(),
                OutletErpId = worksheet.Cells[row, columnIndices["OutletId"]].GetValue<string>(),
                EmployeeErpId = worksheet.Cells[row, columnIndices["EmployeeId"]].GetValue<string>().ToLower(),
                Sequence = worksheet.Cells[row, columnIndices["Sequence"]].GetValue<int>()
            };

            if (record != null)
            {
                records.Add(record);
            }
        }

        return records;
    }

}
