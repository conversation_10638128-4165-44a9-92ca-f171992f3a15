﻿using System.IO;
using System.Threading.Tasks;

namespace Library.StorageWriter.Reader_Writer;

public class HCCBBlobReader : BlobReader
{
    public HCCBBlobReader(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }

    public async Task<Stream> ReadDatBlobContentAsync(string fileName)
    {
        var blob = GetBlobClient(fileName);
        var response = await blob.OpenReadAsync().ConfigureAwait(false);
        return response;
    }
}
