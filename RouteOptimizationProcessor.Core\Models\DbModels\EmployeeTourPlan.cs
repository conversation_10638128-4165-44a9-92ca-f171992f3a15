﻿using Libraries.CommonEnums;

namespace RouteOptimizationProcessor.Core.Models.DbModels;
public class EmployeeTourPlan
{
    public long Id { get; set; }

    public DateTime DeviceTime { get; set; }

    public DateTime ServerTime { get; set; }

    public long EmployeeId { get; set; }

    public long CompanyId { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EffectiveStartDate { get; set; }

    public DateTime EndDate { get; set; }

    public DateTime? ReviewedOn { get; set; }

    public TourPlanApprovedStatus ReviewedStatus { get; set; }

    public bool IsRepeatable { get; set; }
    public int ForDays { get; set; }

    public long? PositionCodeId { get; set; }
    public long? UpdatedFromId { get; set; }
    public long? ReviewedById { get; set; }

    public PortalUserRole? ReviewedByRole { get; set; }

    public virtual ICollection<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }
}

public class EmployeeTourPlanItem
{
    public long Id { get; set; }

    public long EmployeeTourPlanId { get; set; }

    public DateTime ItemDate { get; set; }

    public long? BeatId { get; set; }

    public long? RouteId { get; set; }

    public string ReasonCategory { get; set; }

    public string Reason { get; set; }

    public long? JWFieldUserId { get; set; }
    public long CompanyId { get; set; }
    public long? DistributorId { get; set; }

    public long? JWFieldUserPositionId { get; set; }
}