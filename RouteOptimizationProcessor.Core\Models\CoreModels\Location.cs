﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace RouteOptimizationProcessor.Core.Models.CoreModels;

public class Location
{
    public long Id { get; set; }
    public bool IsBlocked { get; set; }
    public string ShopName { get; set; }
    public OutletSegmentation Segmentation { get; set; }
    public long CompanyId { get; set; }
    public string ErpId { get; set; }
    public long BeatId { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string ShopType { set; get; }
    public OutletChannel OutletChannel { set; get; }
    public OutletSegmentation CompanySegmentation { set; get; }
    public bool IsFocused { set; get; }
    public string ShopTypeCode { get; set; }
    public long? ShopTypeId { get; set; }
    public long? ChannelId { get; set; }

    public override bool Equals(object obj)
    {
        if (this == null)
        {
            return false;
        }
        return this.Equals(obj);
    }
}