﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum ActionOnLeave
{
    [Display(Name = "No Action Taken")]
    NoAction = 0,

    [Display(Name = "Approved")]
    Approved = 1,

    [Display(Name = "Reject")]
    DisApproved = 2
}

public enum LeaveType
{
    PL = 0,
    CL = 1,
    SL = 2,
    LWP = 3
}

public enum EmployeeLeaveApproval
{
    Pending = 0,
    Approve = 1,
    Disapprove = 2,
    Withdraw = 3
}
