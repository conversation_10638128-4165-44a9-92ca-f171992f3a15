﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Library.SlackService;

namespace Library.AsyncLock;

internal sealed class ControlledLock
{
    public ControlledLock(string id, string leaseId, BlobClient blobClient)
    {
        Id = id;
        LeaseId = leaseId;
        BlobClient = blobClient;
    }

    public BlobClient BlobClient { get; set; }
    public string Id { get; set; }
    public string LeaseId { get; set; }
}

public sealed class AzureAsyncLock<T>
{
    private const int waitTimePerAttemptMillis = 2000;
    private readonly BlobContainerClient _container;
    private readonly ErrorMessenger _logger;
    private readonly AutoResetEvent _mutex = new(true);
    private readonly Timer _renewTimer;
    private readonly BlobServiceClient _service;
    private readonly HashSet<ControlledLock> employeesBeingProcessed = new();
    private readonly TimeSpan LockTimeout = TimeSpan.FromMinutes(1);
    private readonly int maxAllowedAttempts;

    public AzureAsyncLock(string connectionString, string containerName, ErrorMessenger _logger, int maxWaitSeconds = 30)
    {
        maxAllowedAttempts = maxWaitSeconds * 1000 / waitTimePerAttemptMillis;
        _service = new BlobServiceClient(connectionString);
        _container = _service.GetBlobContainerClient(containerName);
        _renewTimer = new Timer(RenewLeases, null, RenewInterval, RenewInterval);
        this._logger = _logger;
    }

    private static TimeSpan RenewInterval => TimeSpan.FromSeconds(45);

    private async Task<bool> AcquireLock(T Id, AttemptValidator av)
    {
        while (employeesBeingProcessed.Any(e => e.Id == Id.ToString()) && av.NextAttempt())
        {
            await Task.Delay(waitTimePerAttemptMillis).ConfigureAwait(false);
        }

        if (av.HasExceeded())
        {
            return false;
        }

        var blobClient = _container.GetBlobClient(Id.ToString());

        if (!await blobClient.ExistsAsync().ConfigureAwait(false))
        {
            await blobClient.UploadAsync(BinaryData.FromString(string.Empty), true).ConfigureAwait(false);
        }

        if (_mutex.WaitOne())
        {
            try
            {
                //src: https://github.com/MicrosoftDocs/azure-docs/blob/main/articles/storage/blobs/storage-blob-container-lease.md
                var blc = blobClient.GetBlobLeaseClient();
                var blobLeaseResponse = await blc.AcquireAsync(LockTimeout).ConfigureAwait(false);
                employeesBeingProcessed.Add(new ControlledLock(Id.ToString(), blobLeaseResponse.Value.LeaseId, blobClient));
                return true;
            }
            //src: https://stackoverflow.com/a/64203656/11519765
            catch (RequestFailedException)
            {
                av.NextAttempt();
                return false;
            }
            finally
            {
                _mutex.Set();
            }
        }

        await Task.Delay(waitTimePerAttemptMillis).ConfigureAwait(false);
        av.NextAttempt();
        return false;
    }

    private async Task ReleaseLock(T Id)
    {
        if (_mutex.WaitOne())
        {
            try
            {
                var entry = employeesBeingProcessed.FirstOrDefault(x => x.Id == Id.ToString());
                if (entry != null)
                {
                    try
                    {
                        //src: https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-container-lease
                        var blobLeaseClient = entry.BlobClient.GetBlobLeaseClient(entry.LeaseId);
                        //src: https://github.com/Azure/azure-sdk-for-net/issues/33278
                        var requestConditions = new BlobRequestConditions { LeaseId = entry.LeaseId };
                        var blobLeaseResponse = await blobLeaseClient.ReleaseAsync(requestConditions).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        await _logger.SendToSlack(ex, $"Error releasing lock - {ex.Message}").ConfigureAwait(false);
                    }

                    employeesBeingProcessed.Remove(entry);
                }
            }
            finally
            {
                _mutex.Set();
            }
        }
    }

    private async void RenewLeases(object state)
    {
        if (_mutex.WaitOne())
        {
            try
            {
                if (employeesBeingProcessed.Any())
                {
                    foreach (var entry in employeesBeingProcessed)
                    {
                        await RenewLock(entry).ConfigureAwait(false);
                    }
                }
            }
            catch (Exception ex)
            {
                await _logger.SendToSlack(ex, $"Error renewing leases - {ex.Message}").ConfigureAwait(false);
            }
            finally
            {
                _mutex.Set();
            }
        }
    }

    private async Task RenewLock(ControlledLock entry)
    {
        try
        {
            //src: https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-container-lease
            var blobLeaseClient = entry.BlobClient.GetBlobLeaseClient(entry.LeaseId);
            //src: https://github.com/Azure/azure-sdk-for-net/issues/33278
            var requestConditions = new BlobRequestConditions { LeaseId = entry.LeaseId };
            var blobLeaseResponse = await blobLeaseClient.RenewAsync(requestConditions).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            await _logger.SendToSlack(ex, $"Error renewing lease - {ex.Message}").ConfigureAwait(false);
        }
    }

    public async Task<IDisposable> LockAsync(T Id)
    {
        var av = new AttemptValidator(maxAllowedAttempts);
        while (!await AcquireLock(Id, av).ConfigureAwait(false))
        {
            if (av.HasExceeded())
            {
                throw new MaxLockAttemptExceededException();
            }
        }

        return new Releaser(this, Id);
    }

    private sealed class AttemptValidator
    {
        private readonly int maxAttempts;
        private int currentAttempts;

        public AttemptValidator(int maxAttempts)
        {
            this.maxAttempts = maxAttempts;
        }

        public bool HasExceeded()
        {
            return currentAttempts > maxAttempts;
        }

        public bool NextAttempt()
        {
            currentAttempts++;
            return !HasExceeded();
        }
    }

    private sealed class Releaser : IDisposable
    {
        private readonly AzureAsyncLock<T> asyncLock;
        private readonly T employeeToRealease;

        internal Releaser(AzureAsyncLock<T> asyncLock, T employeeToRealease)
        {
            this.asyncLock = asyncLock;
            this.employeeToRealease = employeeToRealease;
        }

        public void Dispose()
        {
            asyncLock.ReleaseLock(employeeToRealease).Wait();
        }
    }
}
