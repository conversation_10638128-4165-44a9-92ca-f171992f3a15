﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using FileGenerator.Attributes;
using FileGenerator.DataTableHelpers;
using FileGenerator.HelperModels;
using FileGenerator.Interfaces;
using Libraries.CommonEnums;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace FileGenerator;

public class ExcelGeneratorOptimized
{
    private readonly bool _showAllColumns;
    private readonly bool _useNomenclature;
    private readonly Dictionary<string, object> companySettings;
    private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
    private readonly Dictionary<string, bool> selectedCols;

    public ExcelGeneratorOptimized(CompanyNomenclatureSpecifier nomenclatureSpecifier = null, bool showAllColumns = false,
        Dictionary<string, object> companySettings = null,
        Dictionary<string, bool> selectedCols = null)
    {
        this.nomenclatureSpecifier = nomenclatureSpecifier;
        _useNomenclature = nomenclatureSpecifier != null;
        _showAllColumns = showAllColumns;
        this.companySettings = companySettings;
        this.selectedCols = selectedCols;
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    #region Excel Formatting functions

    public static void AutoFitColumns(ExcelWorksheet worksheet)
    {
        try
        {
            if (worksheet.Dimension != null)
            {
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            Console.WriteLine("Failed to AutoFit Columns");
        }
    }

    public static void FormatWorkSheet(ExcelWorksheet worksheet)
    {
        var headerRow = 1;
        worksheet.Row(headerRow).Style.Font.Bold = true;
        worksheet.Row(headerRow).Style.Fill.PatternType = ExcelFillStyle.Solid;
        worksheet.Row(headerRow).Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));
        if (worksheet.Dimension != null)
        {
            worksheet.Cells[worksheet.Dimension.Address].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            var border = worksheet.Cells[worksheet.Dimension.Address].Style.Border;
            border.Left.Style = ExcelBorderStyle.Thin;
            border.Right.Style = ExcelBorderStyle.Thin;
            border.Top.Style = ExcelBorderStyle.Thin;
            border.Bottom.Style = ExcelBorderStyle.Thin;
        }
    }

    #endregion

    #region Public region

    public Dictionary<int, bool> CreateExcel<T>(IEnumerable<T> data, ExcelWorksheet worksheet,
        Dictionary<int, bool> showDataForHeader, int startrowIndex = 2, bool datafetchedInParts = false)
    {
        if (data == null || !data.Any())
        {
            return showDataForHeader;
        }

        var col = 1;
        var allColumns = data.FirstOrDefault()?.GetType().GetProperties().Where(p =>
            Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
        var columns = selectedCols != null && selectedCols.Count > 0
            ? allColumns.Where(p => selectedCols.ContainsKey(p.Name))
            : allColumns;

        var itr_columns = columns.ToArray();
        if (columns == null)
        {
            return showDataForHeader;
        }

        var i = 0;
        var prevgroup = ColGroupType.None;
        var isPrevGroupColored = false;
        var columnsToColor = new List<TableFieldAttribute>();
        var columnNames = new List<string>();
        if (startrowIndex == 2)
        {
            selectColumnsForGenerationExcel();
            columnsOrdering(showDataForHeader, columnNames, itr_columns);
        }
        else
        {
            var columnsNotSelectedInExcel = showDataForHeader.Where(s => !s.Value).ToDictionary(s => s.Key, s => itr_columns[s.Key]);
            foreach (var colProp in columnsNotSelectedInExcel)
            {
                showDataForHeader[colProp.Key] = columnShouldComeInExcel(colProp.Value);
                if (showDataForHeader[colProp.Key])
                {
                    var colAddress = calculateAddress(colProp.Key);
                    var address = worksheet.Cells[1, colAddress].Address;
                    worksheet.Cells[address].Insert(eShiftTypeInsert.EntireColumn);

                    var Tattr = Attribute.GetCustomAttribute(colProp.Value, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        var Tcolumnname = excelAttrOfT.ColumnName;
                        if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                        {
                            var j = 0;
                            var listforheader = Tcolumnname.Split(' ').ToList();
                            var listforNewHeader = Tcolumnname.Split(' ').ToList();
                            foreach (var item in listforheader)
                            {
                                var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                listforNewHeader[j] = nomenclaturename;
                                j++;
                            }

                            Tcolumnname = string.Join(" ", listforNewHeader);
                        }

                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                        {
                            columnsToColor.Add(excelAttrOfT);
                        }

                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                        {
                            worksheet.Column(colAddress).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                        }

                        worksheet.Cells[1, colAddress].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        worksheet.Cells[1, colAddress].Value = Tcolumnname;
                    }
                }
            }

            columnsOrdering(showDataForHeader, columnNames, itr_columns);
        }

        var dataTable = ListToDataTableConverter.GetDataTableFromList(data, worksheet.Name);
        var orderedDT = ReorderColumns(dataTable, columnNames);
        worksheet.Cells[startrowIndex, 1].LoadFromDataTable(orderedDT, false);

        return showDataForHeader;

        #region Functions regarding columns selection/removal/ordering in excel

        void selectColumnsForGenerationExcel()
        {
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var Tcolumnname = excelAttrOfT.ColumnName;
                    if (_showAllColumns || excelAttrOfT.ColumnRequirement != Requirement.HideIfNull ||
                        !CheckIfColNull(colProp, data) || (CheckIfColNull(colProp, data) && datafetchedInParts))
                    {
                        if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                            !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else if (!_showAllColumns && excelAttrOfT.ColumnRequirement == Requirement.HideIfZero &&
                                 CheckIfColZero(colProp, data))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.Selected &&
                                 !selectedCols.ContainsKey(colProp.Name))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                            {
                                worksheet.Column(col).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                            }

                            if (excelAttrOfT.ColGroupType != ColGroupType.None)
                            {
                                if (prevgroup == excelAttrOfT.ColGroupType)
                                {
                                    if (isPrevGroupColored)
                                    {
                                        worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                        worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                        isPrevGroupColored = true;
                                    }
                                }
                                else
                                {
                                    if (!isPrevGroupColored)
                                    {
                                        worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                        worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                        isPrevGroupColored = true;
                                    }
                                    else
                                    {
                                        isPrevGroupColored = false;
                                    }
                                }

                                prevgroup = excelAttrOfT.ColGroupType;
                            }
                            else
                            {
                                prevgroup = ColGroupType.None;
                                isPrevGroupColored = false;
                            }

                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                            {
                                var j = 0;
                                var listforheader = Tcolumnname.Split(' ').ToList();
                                var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                foreach (var item in listforheader)
                                {
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                    listforNewHeader[j] = nomenclaturename;
                                    j++;
                                }

                                Tcolumnname = string.Join(" ", listforNewHeader);
                            }

                            if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                            {
                                columnsToColor.Add(excelAttrOfT);
                            }

                            worksheet.Cells[1, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            worksheet.Cells[1, col++].Value = Tcolumnname;
                            showDataForHeader.Add(i++, true);
                        }
                    }
                    else
                    {
                        showDataForHeader.Add(i++, false);
                    }
                }
                else
                {
                    showDataForHeader.Add(i++, false);
                }
            }
        }

        int calculateAddress(int val)
        {
            var count = 1;
            for (var k = 0; k < val; k++)
            {
                if (showDataForHeader[k])
                {
                    count++;
                }
            }

            return count;
        }

        bool columnShouldComeInExcel(PropertyInfo colProp)
        {
            var res = false;
            var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
            if (Tattr is TableFieldAttribute excelAttrOfT)
            {
                if (_showAllColumns || excelAttrOfT.ColumnRequirement != Requirement.HideIfNull ||
                    !CheckIfColNull(colProp, data) || (CheckIfColNull(colProp, data) && datafetchedInParts))
                {
                    if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                        !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                    {
                        res = false;
                    }
                    else if (!_showAllColumns && excelAttrOfT.ColumnRequirement == Requirement.HideIfZero &&
                             CheckIfColZero(colProp, data))
                    {
                        res = false;
                    }
                    else if (excelAttrOfT.ColumnRequirement == Requirement.Selected &&
                             !selectedCols.ContainsKey(colProp.Name))
                    {
                        res = false;
                    }
                    else
                    {
                        res = true;
                    }
                }
                else
                {
                    return false;
                }
            }

            return res;
        }

        #endregion
    }

    public Dictionary<int, bool> CreateExcelForSelectColumnsReport<T>(IEnumerable<T> data, ExcelWorksheet worksheet,
        Dictionary<int, bool> showDataForHeader, int startrowIndex = 2, List<string> columnNames = null)
    {
        if (data == null || !data.Any())
        {
            return showDataForHeader;
        }

        var col = 1;
        var allColumns = data.FirstOrDefault()?.GetType().GetProperties().Where(p =>
            Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
        var columns = selectedCols != null && selectedCols.Count > 0
            ? allColumns.Where(p => selectedCols.ContainsKey(p.Name))
            : allColumns;

        var itr_columns = columns.ToArray();
        if (columns == null)
        {
            return showDataForHeader;
        }

        var i = 0;
        var prevgroup = ColGroupType.None;
        var isPrevGroupColored = false;
        var columnsToColor = new List<TableFieldAttribute>();
        if (startrowIndex == 2)
        {
            selectColumnsForGenerationExcel();
            columnsOrdering(showDataForHeader, columnNames, itr_columns);
        }

        columnNames = columnNames ?? new List<string>();
        var dataTable = ListToDataTableConverter.GetDataTableFromList(data, worksheet.Name);
        var orderedDT = ReorderColumns(dataTable, columnNames);
        worksheet.Cells[startrowIndex, 1].LoadFromDataTable(orderedDT, false);

        return showDataForHeader;

        #region Functions regarding columns selection/removal/ordering in excel

        void selectColumnsForGenerationExcel()
        {
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var Tcolumnname = excelAttrOfT.ColumnName;

                    if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                    {
                        worksheet.Column(col).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                    }

                    if (excelAttrOfT.ColGroupType != ColGroupType.None)
                    {
                        if (prevgroup == excelAttrOfT.ColGroupType)
                        {
                            if (isPrevGroupColored)
                            {
                                worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                isPrevGroupColored = true;
                            }
                        }
                        else
                        {
                            if (!isPrevGroupColored)
                            {
                                worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                isPrevGroupColored = true;
                            }
                            else
                            {
                                isPrevGroupColored = false;
                            }
                        }

                        prevgroup = excelAttrOfT.ColGroupType;
                    }
                    else
                    {
                        prevgroup = ColGroupType.None;
                        isPrevGroupColored = false;
                    }

                    if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                    {
                        var j = 0;
                        var listforheader = Tcolumnname.Split(' ').ToList();
                        var listforNewHeader = Tcolumnname.Split(' ').ToList();
                        foreach (var item in listforheader)
                        {
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                            listforNewHeader[j] = nomenclaturename;
                            j++;
                        }

                        Tcolumnname = string.Join(" ", listforNewHeader);
                    }

                    if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                    {
                        columnsToColor.Add(excelAttrOfT);
                    }

                    worksheet.Cells[1, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    worksheet.Cells[1, col++].Value = Tcolumnname;
                    showDataForHeader.Add(i++, true);
                }
                else
                {
                    showDataForHeader.Add(i++, false);
                }
            }
        }

        #endregion
    }

    public ExcelPackage MakePivotedExcel<T>(ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, PivotDetails pivotColumnObject, string IdColumn, string delimiter,
        string defaultGroupColumnName, Dictionary<string, int> sortingDictionaryList, Dictionary<string, string> groupTextDisplayDictionary, IEnumerable<string> columnsToDeleteFromPivot = null)
    {
        excelPackage = excelPackage ?? new ExcelPackage();
        var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
        if (data.Any())
        {
            CreatePivotedWorkSheet(worksheet, data, pivotColumnObject, IdColumn, delimiter, defaultGroupColumnName, sortingDictionaryList, groupTextDisplayDictionary, columnsToDeleteFromPivot);
        }
        else
        {
            CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
        }

        return excelPackage;
    }

    public ExcelPackage MakePivotedExcelWithGroupedData<T>(ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, Dictionary<string, int> sortingDictionaryList = null, Dictionary<string, string> groupTextDisplayDictionary = null,
        IEnumerable<string> columnsToDeleteFromPivot = null, List<string> pivotParentOrder = null, bool isUniqueIdentifier = false, string delimeter = "_")
    {
        excelPackage = excelPackage ?? new ExcelPackage();
        var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
        if (data.Any())
        {
            CreatePivotedWorkSheetWithGroupedData(worksheet, data, sortingDictionaryList, groupTextDisplayDictionary, columnsToDeleteFromPivot, pivotParentOrder, isUniqueIdentifier, delimeter);
        }
        else
        {
            CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
        }

        return excelPackage;
    }

    public ExcelPackage MakePivotedExcelHavingSingleHeaderWithGroupedData<T>(ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, Dictionary<string, int> sortingDictionaryList = null,
        Dictionary<string, string> groupTextDisplayDictionary = null, IEnumerable<string> columnsToDeleteFromPivot = null, List<string> pivotParentOrder = null, bool isUniqueIdentifier = false, bool includeHeaderVal = true)
    {
        excelPackage = excelPackage ?? new ExcelPackage();
        var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
        if (data.Any())
        {
            CreatePivotedWorkSheetHavingSingleHeaderWithGroupedData(worksheet, data, sortingDictionaryList, groupTextDisplayDictionary, columnsToDeleteFromPivot, pivotParentOrder, isUniqueIdentifier,
                includeHeaderVal);
        }
        else
        {
            CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
        }

        return excelPackage;
    }

    public ExcelPackage MakePivotedExcelHavingSingleHeaderWithGroupedDataWithDynamicPivotProperties<T>(ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, Dictionary<string, int> sortingDictionaryList = null,
        Dictionary<string, string> groupTextDisplayDictionary = null, IEnumerable<string> columnsToDeleteFromPivot = null, List<PivotProperty> pivotParentOrder = null, bool isUniqueIdentifier = false, bool includeHeaderVal = true)
    {
        excelPackage = excelPackage ?? new ExcelPackage();
        var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
        if (data.Any())
        {
            CreatePivotedWorkSheetHavingSingleHeaderWithGroupedDataWithDynamicPivotProperties(worksheet, data, sortingDictionaryList, groupTextDisplayDictionary, columnsToDeleteFromPivot, pivotParentOrder, isUniqueIdentifier);
        }
        else
        {
            CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
        }

        return excelPackage;
    }

    /// <summary>
    /// Adds pivot header row with merged cells and formats the specified Excel worksheet, which originally contains flat data with column headers.
    /// Ensure that the order of headers in the pivotingOrder list matches the desired order in the Excel sheet.
    /// </summary>
    /// <param name="workSheet">The <see cref="ExcelWorksheet"/> to which the pivot header and formatting will be added.</param>
    /// <param name="pivotingOrder">A list of lists, where each inner list contains headers for a pivot group. These headers will be placed in the merged cells for each corresponding pivot.</param>
    /// <param name="pivotColumnCount">A list of integers specifying the number of columns to merge for each pivot group. The count of this list must match the count of pivotingOrder list.</param>
    /// <returns>The formatted <see cref="ExcelWorksheet"/> with the added pivot header</returns>
    public static ExcelWorksheet AddPivotHeaderAndFormatCustomExcel(ExcelWorksheet workSheet, List<List<string>> pivotingOrder, List<int> pivotColumnCount)
    {
        if (workSheet.Dimension == null)
        {
            return workSheet;
        }

        workSheet.InsertRow(1, 1);
        var col = 1;
        for (var i = 0; i < pivotColumnCount.Count; i++)
        {
            var mergeCol = pivotColumnCount[i];
            if (pivotingOrder[i].Count > 0)
            {
                foreach (var item in pivotingOrder[i])
                {
                    workSheet.Cells[1, col, 1, col + mergeCol - 1].Merge = true;
                    workSheet.Cells[1, col].Value = item;
                    col += mergeCol;
                }
            }
            else
            {
                workSheet.Cells[1, col, 1, col + mergeCol - 1].Merge = true;
                col += mergeCol;
            }
        }

        #region formatting

        var headerRow = 2;
        while (headerRow > 0)
        {
            workSheet.Row(headerRow).Style.Font.Bold = true;
            workSheet.Row(headerRow).Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRow--;
        }

        workSheet.Row(1).Style.Fill.BackgroundColor.SetColor(Color.FromArgb(211, 211, 211));
        workSheet.Row(2).Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));

        workSheet.Cells[workSheet.Dimension.Address].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        var border = workSheet.Cells[workSheet.Dimension.Address].Style.Border;
        border.Left.Style = ExcelBorderStyle.Thin;
        border.Right.Style = ExcelBorderStyle.Thin;
        border.Top.Style = ExcelBorderStyle.Thin;
        border.Bottom.Style = ExcelBorderStyle.Thin;

        #endregion

        return workSheet;
    }

    #endregion

    #region private

    private static ExcelWorksheet CreateExcelForErrorAndNoData(ExcelWorksheet worksheet, string errorMessage)
    {
        worksheet.Cells[1, 1].LoadFromText(errorMessage);
        return worksheet;
    }

    private static (DataTable pivotTable, List<string> unpivotedColumns) PivotDataTableToExcel(DataTable dataTable, PivotDetails pivotColumn, string IdColumn, string delimiter, string defaultGroupColumnName,
        Dictionary<string, int> sortingDictionaryList)
    {
        // Pivot the DataTable
        var pivotTable = new DataTable();
        var allcolumns = dataTable.Columns.Cast<DataColumn>().Select(d => d.ColumnName).ToList();
        var unpivotedColumns = allcolumns.Where(c => c != IdColumn && c != pivotColumn.ParentColumn && !pivotColumn.ValueColumns.Contains(c)).ToList();
        foreach (var c in unpivotedColumns)
        {
            pivotTable.Columns.Add(c, dataTable.Columns[c].DataType);
        }

        // Get distinct ParentValues
        var distinctParentValues = dataTable.AsEnumerable().Select(r => r.Field<string>(pivotColumn.ParentColumn)).Distinct().ToList();

        // Add columns for each distinct ParentValues
        foreach (var parentValue in distinctParentValues)
        {
            foreach (var c in pivotColumn.ValueColumns)
            {
                pivotTable.Columns.Add(parentValue + delimiter + c, dataTable.Columns[c].DataType);
            }
        }

        foreach (var rowGroup in dataTable.Rows.Cast<DataRow>().GroupBy(r => r[IdColumn]))
        {
            var pivotRow = pivotTable.NewRow();
            var defaultRow = rowGroup.FirstOrDefault();
            foreach (var c in unpivotedColumns)
            {
                pivotRow[c] = defaultRow[c];
            }

            foreach (var row in rowGroup)
            {
                var parentValue = row[pivotColumn.ParentColumn].ToString();

                foreach (var c in pivotColumn.ValueColumns)
                {
                    //Addition Functionality is Failing with null Values. Will handle it when we need it
                    //if (parentValue != defaultGroupColumnName && pivotTable.Columns[parentValue + delimiter + c].IsNumeric())
                    //    pivotRow[parentValue + delimiter + c] = AddTwoObjectsOfSameType(pivotRow[parentValue + delimiter + c], row[c]);
                    //else
                    pivotRow[parentValue + delimiter + c] = row[c];
                }
            }

            pivotTable.Rows.Add(pivotRow);
        }

        var columnsInPivotTable = new HashSet<string>(pivotTable.Columns.Cast<DataColumn>().Select(c => c.ColumnName));
        unpivotedColumns.RemoveAll(c => !columnsInPivotTable.Contains(c));
        if (sortingDictionaryList != null)
        {
            foreach (var pair in sortingDictionaryList)
            {
                if (pivotTable.Columns.Contains(pair.Key))
                {
                    pivotTable.Columns[pair.Key].SetOrdinal(unpivotedColumns.Count + pair.Value - 1);
                }
            }
        }

        //Remove Columns with default Text
        foreach (var c in pivotColumn.ValueColumns)
        {
            if (pivotTable.Columns.Contains(defaultGroupColumnName + delimiter + c))
            {
                pivotTable.Columns.Remove(defaultGroupColumnName + delimiter + c);
            }
        }

        return (pivotTable, unpivotedColumns);
    }

    private int CreateExcelFromDataTable<T>(ExcelWorksheet worksheet, DataTable dataTable, PivotDetails pivotDetails = null, Dictionary<string, string> groupTextDisplayDictionary = null, List<string> unpivotedColumns = null)
    {
        var pivotStart = 0;
        var columns = typeof(T).GetProperties();
        var setOrdinal = 0;
        //const string delimiterForColumnColission = "$%$";
        foreach (var colProp in columns)
        {
            var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
            var isExcelAttributeDefined = Tattr is TableFieldAttribute _;
            if (isExcelAttributeDefined)
            {
                var excelAttrOfT = Tattr as TableFieldAttribute;
                var columnname = excelAttrOfT.ColumnName;
                //Deletes null columns
                if (dataTable.Columns.Contains(colProp.Name))
                {
                    var columnRemoved = false;
                    if (!_showAllColumns)
                    {
                        if (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && dataTable.Rows
                                .Cast<DataRow>().All(dr => string.IsNullOrEmpty(dr[colProp.Name].ToString())))
                        {
                            dataTable.Columns.Remove(colProp.Name);
                            columnRemoved = true;
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.HideIfZero &&
                                 Convert.ToDouble(dataTable.Compute("Max(" + colProp.Name + ")", string.Empty)) == 0)
                        {
                            dataTable.Columns.Remove(colProp.Name);
                            columnRemoved = true;
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                                 !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                        {
                            dataTable.Columns.Remove(colProp.Name);
                            columnRemoved = true;
                        }
                    }

                    if (!columnRemoved)
                    {
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                        {
                            worksheet.Column(setOrdinal + 1).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                        }

                        dataTable.Columns[colProp.Name].SetOrdinal(setOrdinal);

                        if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                        {
                            columnname = string.Join(" ", columnname.Split(' ').Select(item => nomenclatureSpecifier.GetHeaderName(item)));
                        }

                        //if(columnname != colProp.Name && dataTable.Columns.Contains(columnname))
                        //{
                        //    dataTable.Columns[colProp.Name].ColumnName = colProp.Name + delimiterForColumnColission + columnname;
                        //}
                        //else
                        dataTable.Columns[colProp.Name].Caption = columnname;
                        setOrdinal++;
                        pivotStart++;
                    }
                }
            }
            else
            {
                if (dataTable.Columns.Contains(colProp.Name))
                {
                    dataTable.Columns.Remove(colProp.Name);
                }
            }
        }

        if (pivotDetails != null && groupTextDisplayDictionary != null && pivotDetails.ValueColumns.Count == 1)
        {
            foreach (var pair in groupTextDisplayDictionary)
            {
                if (dataTable.Columns.Contains(pair.Key))
                {
                    dataTable.Columns[pair.Key].Caption = pair.Value;
                }
            }
        }

        //foreach(var c in dataTable.Columns.Cast<DataColumn>())
        //{
        //    c.Caption = c.ColumnName.Split(delimiterForColumnColission.ToArray()).LastOrDefault();
        //}
        worksheet.Cells.LoadFromDataTable(dataTable, true);

        return pivotStart;
    }

    private ExcelWorksheet CreatePivotedWorkSheet<T>(ExcelWorksheet workSheet, IEnumerable<T> data, PivotDetails pivotDetails, string IdColumn, string delimiter,
        string defaultGroupColumnName, Dictionary<string, int> sortingDictionaryList, Dictionary<string, string> groupTextDisplayDictionary, IEnumerable<string> columnsToDeleteFromPivot = null)
    {
        var ConvertToDataTable = new ListToDataTableConverter();
        var dt = ListToDataTableConverter.GetDataTableFromList(data, "tempName1");
        if (columnsToDeleteFromPivot != null && columnsToDeleteFromPivot.Any())
        {
            foreach (var col in columnsToDeleteFromPivot)
            {
                dt.Columns.Remove(col);
                pivotDetails.ValueColumns.Remove(col);
            }
        }

        var (pivotTable, unpivotedColumns) = PivotDataTableToExcel(dt, pivotDetails, IdColumn, delimiter, defaultGroupColumnName, sortingDictionaryList);
        CreateExcelFromDataTable<T>(workSheet, pivotTable, pivotDetails, groupTextDisplayDictionary, unpivotedColumns);
        FormatWorkSheet(workSheet);
        AutoFitColumns(workSheet);
        return workSheet;
    }

    private ExcelWorksheet CreatePivotedWorkSheetWithGroupedData<T>(ExcelWorksheet workSheet, IEnumerable<T> data,
        Dictionary<string, int> sortingDictionaryList, Dictionary<string, string> groupTextDisplayDictionary, IEnumerable<string> columnsToDeleteFromPivot = null, List<string> pivotParentOrder = null, bool isUniqueIdentifier = false,
        string delimeter = "_")
    {
        var ungroupedDynamicList = ConvertGroupedListToDynamicUngroupedList(data, pivotParentOrder, delimeter: delimeter);
        var ConvertToDataTable = new ListToDataTableConverter();
        var pivotTable = ListToDataTableConverter.GetDataTableFromListOfDicNew(ungroupedDynamicList);
        CreateExcelFromDataTable<T>(workSheet, pivotTable, groupTextDisplayDictionary: groupTextDisplayDictionary);

        FormatWorkSheet(workSheet);
        AddPivotParent(workSheet, delimeter, isUniqueIdentifier);
        AutoFitColumns(workSheet);
        return workSheet;
    }

    private ExcelWorksheet CreatePivotedWorkSheetHavingSingleHeaderWithGroupedData<T>(ExcelWorksheet workSheet, IEnumerable<T> data,
        Dictionary<string, int> sortingDictionaryList, Dictionary<string, string> groupTextDisplayDictionary, IEnumerable<string> columnsToDeleteFromPivot = null, List<string> pivotParentOrder = null, bool isUniqueIdentifier = false,
        bool includeHeaderVal = false)
    {
        var ungroupedDynamicList = ConvertGroupedListToDynamicUngroupedList(data, pivotParentOrder, includeHeaderVal);
        var ConvertToDataTable = new ListToDataTableConverter();
        var pivotTable = ListToDataTableConverter.GetDataTableFromListOfDicNew(ungroupedDynamicList);
        var pivotStart = CreateExcelFromDataTable<T>(workSheet, pivotTable, groupTextDisplayDictionary: groupTextDisplayDictionary);

        if (isUniqueIdentifier)
        {
            RemoveUniqueIdentifierFromHeaderRow(workSheet, pivotStart);
        }

        FormatWorkSheet(workSheet);
        AutoFitColumns(workSheet);
        return workSheet;
    }

    private static void InsertHyperLinksForImageUrl(ExcelWorksheet worksheet, int pivotStart, List<PivotProperty> pivotParentOrder)
    {
        var idx = 1;
        foreach (var prop in pivotParentOrder)
        {
            if (prop.propertyType == DataTypeAttribute.Url)
            {
                var colNo = pivotStart + idx;
                var endRow = worksheet.Dimension.End.Row;
                for (var row = 2; row <= endRow; row++)
                {
                    var cell = worksheet.Cells[row, colNo];
                    var url = cell.Text;

                    if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
                    {
                        cell.Hyperlink = new Uri(url);
                        cell.Style.Font.UnderLine = true;
                        cell.Style.Font.Color.SetColor(Color.Blue);
                    }
                }
            }

            idx++;
        }
    }

    private ExcelWorksheet CreatePivotedWorkSheetHavingSingleHeaderWithGroupedDataWithDynamicPivotProperties<T>(ExcelWorksheet workSheet, IEnumerable<T> data,
        Dictionary<string, int> sortingDictionaryList, Dictionary<string, string> groupTextDisplayDictionary, IEnumerable<string> columnsToDeleteFromPivot = null, List<PivotProperty> pivotParentOrder = null, bool isUniqueIdentifier = false)
    {
        var ungroupedDynamicList = ConvertGroupedListToDynamicUngroupedList(data, pivotParentOrder, false);
        var ConvertToDataTable = new ListToDataTableConverter();
        var pivotTable = ListToDataTableConverter.GetDataTableFromListOfDicNew(ungroupedDynamicList);
        var pivotStart = CreateExcelFromDataTable<T>(workSheet, pivotTable, groupTextDisplayDictionary: groupTextDisplayDictionary);

        if (isUniqueIdentifier)
        {
            RemoveUniqueIdentifierFromHeaderRow(workSheet, pivotStart);
        }

        InsertHyperLinksForImageUrl(workSheet, pivotStart, pivotParentOrder);
        FormatWorkSheet(workSheet);
        AutoFitColumns(workSheet);
        return workSheet;
    }

    private static List<IDictionary<string, object>> ConvertGroupedListToDynamicUngroupedList<T>(IEnumerable<T> data, List<string> pivotOrder, bool includeHeadedVal = true, string delimeter = "_")
    {
        return data.Select(d =>
        {
            var dynamicObject = new ExpandoObject() as IDictionary<string, object>;

            foreach (var property in d.GetType().GetProperties())
            {
                if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition() == typeof(List<>)) //
                {
                    if (property.GetValue(d) is IEnumerable<object> listValue)
                    {
                        var index = 0;

                        foreach (var listItem in listValue)
                        {
                            foreach (var listItemProperty in listItem.GetType().GetProperties())
                            {
                                if (includeHeadedVal)
                                {
                                    dynamicObject[$"{pivotOrder[index]}{delimeter}{listItemProperty.Name}"] = listItemProperty.GetValue(listItem);
                                }
                                else
                                {
                                    dynamicObject[$"{pivotOrder[index]}"] = listItemProperty.GetValue(listItem);
                                }
                            }

                            index++;
                        }
                    }
                }
                else
                {
                    dynamicObject[property.Name] = property.GetValue(d);
                }
            }

            return dynamicObject;
        }).ToList();
    }

    private static List<IDictionary<string, object>> ConvertGroupedListToDynamicUngroupedList<T>(IEnumerable<T> data, List<PivotProperty> pivotOrder, bool includeHeadedVal = true)
    {
        return data.Select(d =>
        {
            var dynamicObject = new ExpandoObject() as IDictionary<string, object>;

            foreach (var property in d.GetType().GetProperties())
            {
                if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition() == typeof(List<>))
                {
                    if (property.GetValue(d) is IEnumerable<object> listValue)
                    {
                        var index = 0;

                        foreach (var listItem in listValue)
                        {
                            foreach (var listItemProperty in listItem.GetType().GetProperties())
                            {
                                dynamicObject[$"{pivotOrder[index].Name}"] = listItemProperty.GetValue(listItem);
                            }

                            index++;
                        }
                    }
                }
                else
                {
                    dynamicObject[property.Name] = property.GetValue(d);
                }
            }

            return dynamicObject;
        }).ToList();
    }

    private bool CheckIfColRequiredBySetting(PropertyInfo colProp, CompanySetting[] companySettingsToCheck)
    {
        if (companySettings != null && companySettingsToCheck != null && companySettingsToCheck.Length > 0)
        {
            foreach (var companySetting in companySettingsToCheck)
            {
                switch (companySetting)
                {
                    case CompanySetting.UsesPositionCodes:
                        if (!companySettings.ContainsKey(companySetting.ToString()) ||
                            !(bool)companySettings[companySetting.ToString()])
                        {
                            return false;
                        }

                        break;

                    case CompanySetting.HighestPositionLevel:
                        PositionCodeLevel colPosLevel, highestPosLevel;
                        if (!companySettings.ContainsKey(companySetting.ToString()) ||
                            !Enum.TryParse("Level" + colProp.Name[1], out colPosLevel) ||
                            !Enum.TryParse(companySettings[companySetting.ToString()].ToString(),
                                out highestPosLevel) || highestPosLevel > colPosLevel)
                        {
                            return false;
                        }

                        break;

                    case CompanySetting.HighestGeoHierarchy:
                        GeographyLevel colGeoLevel, highestGeoLevel;
                        if (!companySettings.ContainsKey(companySetting.ToString()) ||
                            !Enum.TryParse(colProp.Name, out colGeoLevel) ||
                            !Enum.TryParse(companySettings[companySetting.ToString()].ToString(),
                                out highestGeoLevel) || highestGeoLevel < colGeoLevel)
                        {
                            return false;
                        }

                        break;

                    case CompanySetting.UsesFAUnify:
                        if (!companySettings.ContainsKey(companySetting.ToString()) ||
                            !(bool)companySettings[companySetting.ToString()])
                        {
                            return false;
                        }

                        break;

                    case CompanySetting.NotApplicable:
                    default:
                        return true;
                }
            }
        }

        return true;
    }

    private static bool CheckIfColNull<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
    {
        foreach (var item in listOfData)
        {
            if (colProp.GetValue(item) != null && !string.IsNullOrWhiteSpace(colProp.GetValue(item).ToString()))
            {
                return false;
            }
        }

        return true;
    }

    private static bool CheckIfColZero<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
    {
        decimal value;
        double value2;
        foreach (var item in listOfData)
        {
            if (colProp.GetValue(item) != null && decimal.TryParse(colProp.GetValue(item).ToString(), out value) &&
                value > 0)
            {
                return false;
            }

            if (colProp.GetValue(item) != null &&
                double.TryParse(colProp.GetValue(item).ToString(), out value2) && value2 > 0)
            {
                return false;
            }
        }

        return true;
    }

    private static void columnsOrdering(Dictionary<int, bool> showDataForHeader, List<string> columnNames, PropertyInfo[] itr_columns)
    {
        for (var j = 0; j < itr_columns.Length; j++)
        {
            if (showDataForHeader[j])
            {
                columnNames.Add(itr_columns[j].Name);
            }
        }
    }

    private static DataTable ReorderColumns(DataTable originalDataTable, List<string> desiredColumnOrder)
    {
        var orderedDataTable = new DataTable();

        // Add columns to the new DataTable in the desired order
        foreach (var columnName in desiredColumnOrder)
        {
            if (originalDataTable.Columns.Contains(columnName))
            {
                orderedDataTable.Columns.Add(columnName, originalDataTable.Columns[columnName].DataType);
            }
        }

        // Copy data from original DataTable to the ordered DataTable
        foreach (DataRow originalRow in originalDataTable.Rows)
        {
            var newRow = orderedDataTable.NewRow();
            foreach (DataColumn column in orderedDataTable.Columns)
            {
                newRow[column.ColumnName] = originalRow[column.ColumnName];
            }

            orderedDataTable.Rows.Add(newRow);
        }

        return orderedDataTable;
    }

    private static void FormatPivotRow(ExcelWorksheet worksheet, int currpivotStart, int row = 1)
    {
        var totalCols = worksheet.Dimension.End.Column;
        worksheet.Cells[row, currpivotStart].Style.Font.Bold = true;
        worksheet.Cells[row, currpivotStart].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        worksheet.Cells[row, currpivotStart].Style.Fill.PatternType = ExcelFillStyle.Solid;
        worksheet.Cells[row, currpivotStart].Style.Border.BorderAround(ExcelBorderStyle.Medium);
        worksheet.Cells[row, currpivotStart].Style.Fill.BackgroundColor.SetColor(Color.LightGray);

        var border = worksheet.Cells[worksheet.Dimension.Address].Style.Border;
        border.Left.Style = ExcelBorderStyle.Thin;
        border.Right.Style = ExcelBorderStyle.Thin;
        border.Top.Style = ExcelBorderStyle.Thin;
        border.Bottom.Style = ExcelBorderStyle.Thin;
    }

    private static void AddPivotParent(ExcelWorksheet worksheet, string delimiter = "_", bool isUniqueIdentifier = false)
    {
        worksheet.InsertRow(1, 1);
        var totalCols = worksheet.Dimension.End.Column;
        var currpivotStart = 1;
        var currpivotColName = "";

        if (isUniqueIdentifier)
        {
            for (var col = 1; col <= totalCols; col++)
            {
                var originalHeader = worksheet.Cells[2, col].Text;
                var splittedHeader = originalHeader.Split(delimiter);

                if (splittedHeader.Length >= 2)
                {
                    var newHeader = string.Join(delimiter, splittedHeader.Take(splittedHeader.Length - 1));
                    if (currpivotColName != newHeader)
                    {
                        if (currpivotColName != "")
                        {
                            worksheet.Cells[1, currpivotStart, 1, col - 1].Merge = true;
                            worksheet.Cells[1, currpivotStart].Value = currpivotColName.Split(delimiter)[0];
                            FormatPivotRow(worksheet, currpivotStart);
                        }

                        currpivotStart = col;
                        currpivotColName = newHeader;
                    }

                    if (col == totalCols)
                    {
                        worksheet.Cells[1, currpivotStart, 1, col].Merge = true;
                        worksheet.Cells[1, currpivotStart].Value = splittedHeader[0];
                        FormatPivotRow(worksheet, currpivotStart);
                    }

                    worksheet.Cells[2, col].Value = splittedHeader.Last();
                }
            }
        }
        else
        {
            for (var col = 1; col <= totalCols; col++)
            {
                var originalHeader = worksheet.Cells[2, col].Text;
                var splittedHeader = originalHeader.Split(delimiter);
                //var newHeader = originalHeader.Replace(delimiter, " ");

                if (splittedHeader.Length == 2)
                {
                    if (currpivotColName != splittedHeader[0])
                    {
                        if (currpivotColName != "")
                        {
                            worksheet.Cells[1, currpivotStart, 1, col - 1].Merge = true;
                            worksheet.Cells[1, currpivotStart].Value = currpivotColName;
                            FormatPivotRow(worksheet, currpivotStart);
                        }

                        currpivotStart = col;
                        currpivotColName = splittedHeader[0];
                    }

                    if (col == totalCols)
                    {
                        worksheet.Cells[1, currpivotStart, 1, col].Merge = true;
                        worksheet.Cells[1, currpivotStart].Value = currpivotColName;
                        FormatPivotRow(worksheet, currpivotStart);
                    }

                    worksheet.Cells[2, col].Value = splittedHeader[1];
                }
            }
        }
    }

    private static void RemoveUniqueIdentifierFromHeaderRow(ExcelWorksheet worksheet, int pivotStart, string delimiter = "$$")
    {
        var totalCols = worksheet.Dimension.End.Column;
        for (var col = pivotStart; col <= totalCols; col++)
        {
            var originalHeader = worksheet.Cells[1, col].Text;
            var splittedHeader = originalHeader.Split(delimiter);
            worksheet.Cells[1, col].Value = splittedHeader[0];
        }
    }

    #endregion
}
