﻿using System;
using System.Collections.Generic;
using System.Linq;
using Libraries.CommonEnums;
using static Libraries.CommonEnums.TypesUsedInReports;

namespace Library.SqlHelper;

public static class QueryHelpers
{
    public static class EmployeeHierrachyFilters
    {
        [Obsolete]
        public static string EmployeeHierarchyNewIds(PortalUserRole userRole, long userId)
        {
            return $@"ISNULL(GSMUserId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.GlobalSalesManager}' THEN {userId}
                    ELSE  ISNULL(GSMUserId,0)
                    END
                    AND
                    ISNULL(NSMUserId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.NationalSalesManager}' THEN {userId}
                    ELSE  ISNULL(NSMUserId,0)
                    END
                    AND
                    ISNULL(ZSMUserId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.ZonalSalesManager}' THEN {userId}
                    ELSE ISNULL(ZSMUserId,0)
                    END
                    AND
                    ISNULL(RSMUserId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.RegionalSalesManager}' THEN {userId}
                    ELSE ISNULL(RSMUserId,0)
                    END
                    AND
                    ISNULL(ASMUserId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.AreaSalesManager}' THEN {userId}
                    ELSE ISNULL(ASMUserId,0)
                    END
                    AND
                    ESMId = CASE
                    WHEN '{userRole}'='{PortalUserRole.ClientEmployee}' THEN {userId}
                    ELSE ESMId
                    END";
        }

        /// <summary>
        /// Use following SqlParameters before adding this in query
        /// 1. userId [long]
        /// 2. userRole [string]
        /// </summary>
        /// <returns>Constant string</returns>
        public static string GetEmployeeHierarchyFilterWithSqlParameter()
        {
            return $@"ISNULL(GSMUserId,0) = CASE
                    WHEN '@userRole'='{PortalUserRole.GlobalSalesManager}' THEN @userId
                    ELSE  ISNULL(GSMUserId,0)
                    END
                    AND
                    ISNULL(NSMUserId,0) = CASE
                    WHEN '@userRole'='{PortalUserRole.NationalSalesManager}' THEN @userId
                    ELSE  ISNULL(NSMUserId,0)
                    END
                    AND
                    ISNULL(ZSMUserId,0) = CASE
                    WHEN '@userRole'='{PortalUserRole.ZonalSalesManager}' THEN @userId
                    ELSE ISNULL(ZSMUserId,0)
                    END
                    AND
                    ISNULL(RSMUserId,0) = CASE
                    WHEN '@userRole'='{PortalUserRole.RegionalSalesManager}' THEN @userId
                    ELSE ISNULL(RSMUserId,0)
                    END
                    AND
                    ISNULL(ASMUserId,0) = CASE
                    WHEN '@userRole'='{PortalUserRole.AreaSalesManager}' THEN @userId
                    ELSE ISNULL(ASMUserId,0)
                    END
                    AND
                    ESMId = CASE
                    WHEN '@userRole'='{PortalUserRole.ClientEmployee}' THEN @userId
                    ELSE ESMId
                    END";
        }

        public static string EmployeeHierarchyIdNew(PortalUserRole userRole, long userId)
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return $@"GSMUserId = {userId}";

                case PortalUserRole.NationalSalesManager:
                    return $@"NSMUserId = {userId}";

                case PortalUserRole.ZonalSalesManager:
                    return $@"ZSMUserId = {userId}";

                case PortalUserRole.RegionalSalesManager:
                    return $@"RSMUserId = {userId}";

                case PortalUserRole.AreaSalesManager:
                    return $@"ASMUserId = {userId}";

                case PortalUserRole.ClientEmployee:
                    return $@"ESMId = {userId}";

                default: return "1=1";
            }
        }

        public static string ManagerIdsInString(PortalUserRole userRole, string placeholder = "$$Ids$$")
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return $@"GSMId in ({placeholder})";

                case PortalUserRole.NationalSalesManager:
                    return $@"NSMId in ({placeholder})";

                case PortalUserRole.ZonalSalesManager:
                    return $@"ZSMId in ({placeholder})";

                case PortalUserRole.RegionalSalesManager:
                    return $@"RSMId in ({placeholder})";

                case PortalUserRole.AreaSalesManager:
                    return $@"ASMId in ({placeholder})";

                case PortalUserRole.ClientEmployee:
                    return $@"ESMId in ({placeholder})";

                default: return "1=1";
            }
        }

        public static string DMSManagerIdsInString(PortalUserRole userRole, string placeholder = "$$Ids$$")
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return $@"GSMId in ({placeholder})";

                case PortalUserRole.NationalSalesManager:
                    return $@"NSMId in ({placeholder})";

                case PortalUserRole.ZonalSalesManager:
                    return $@"ZSMId in ({placeholder})";

                case PortalUserRole.RegionalSalesManager:
                    return $@"RSMId in ({placeholder})";

                case PortalUserRole.AreaSalesManager:
                    return $@"ASMId in ({placeholder})";

                case PortalUserRole.ClientEmployee:
                    return $@"EmployeeId in ({placeholder})";

                default: return "1=1";
            }
        }

        public static string ManagerIds(PortalUserRole userRole)
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return @"GSMId";

                case PortalUserRole.NationalSalesManager:
                    return @"NSMId";

                case PortalUserRole.ZonalSalesManager:
                    return @"ZSMId";

                case PortalUserRole.RegionalSalesManager:
                    return @"RSMId";

                case PortalUserRole.AreaSalesManager:
                    return @"ASMId";

                case PortalUserRole.ClientEmployee:
                    return @"ESMId";

                default: return "ESMId";
            }
        }

        public static string ManagerNewIds(PortalUserRole userRole)
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return @"GSMUSerId";

                case PortalUserRole.NationalSalesManager:
                    return @"NSMUserId";

                case PortalUserRole.ZonalSalesManager:
                    return @"ZSMUserId";

                case PortalUserRole.RegionalSalesManager:
                    return @"RSMUserId";

                case PortalUserRole.AreaSalesManager:
                    return @"ASMUserId";

                case PortalUserRole.ClientEmployee:
                    return @"ESMId";

                default: return "ESMId";
            }
        }

        public static string ManagerNewIdsInString(PortalUserRole userRole, string placeholder = "$$Ids$$")
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return $@"GSMUserId in ({placeholder})";

                case PortalUserRole.NationalSalesManager:
                    return $@"NSMUserId in ({placeholder})";

                case PortalUserRole.ZonalSalesManager:
                    return $@"ZSMUserId in ({placeholder})";

                case PortalUserRole.RegionalSalesManager:
                    return $@"RSMUserId in ({placeholder})";

                case PortalUserRole.AreaSalesManager:
                    return $@"ASMUserId in ({placeholder})";

                case PortalUserRole.ClientEmployee:
                    return $@"ESMId in ({placeholder})";

                default: return "1=1";
            }
        }

        public static string ManagerNewIdsByPosInString(PositionCodeLevel? PositionCodeLevelFIlter, string placeholder = "$$Ids$$")
        {
            switch (PositionCodeLevelFIlter)
            {
                case PositionCodeLevel.L8Position:
                    return $@"AND PositionLevel8UserId in ({placeholder})";

                case PositionCodeLevel.L7Position:
                    return $@"AND PositionLevel7UserId in ({placeholder})";

                case PositionCodeLevel.L6Position:
                    return $@"AND PositionLevel6UserId in ({placeholder})";

                case PositionCodeLevel.L5Position:
                    return $@"AND PositionLevel5UserId in ({placeholder})";

                case PositionCodeLevel.L4Position:
                    return $@"AND PositionLevel4UserId in ({placeholder})";

                case PositionCodeLevel.L3Position:
                    return $@"AND PositionLevel3UserId in ({placeholder})";

                case PositionCodeLevel.L2Position:
                    return $@"AND PositionLevel2UserId in ({placeholder})";

                case PositionCodeLevel.L1Position:
                    return $@"AND PositionLevel1UserId in ({placeholder})";

                default: return "AND 1=1";
            }
        }

        public static string EmployeeHierarchyOldIds(PortalUserRole userRole, string placeholder = "$$Ids$$")
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return $@"AND GSMId in ({placeholder})";

                case PortalUserRole.NationalSalesManager:
                    return $@"AND NSMId in ({placeholder})";

                case PortalUserRole.ZonalSalesManager:
                    return $@"AND ZSMId in ({placeholder})";

                case PortalUserRole.RegionalSalesManager:
                    return $@"AND RSMId in ({placeholder})";

                case PortalUserRole.AreaSalesManager:
                    return $@"AND ASMId in ({placeholder})";

                case PortalUserRole.ClientEmployee:
                    return $@"AND ESMId in ({placeholder})";

                default:
                    return "";
            }
        }

        public static string EmployeeHierarchyOldIds(PortalUserRole userRole, List<long> userIds)
        {
            var placeholder = string.Join(",", userIds);
            switch (userRole)
            {
                case PortalUserRole.GlobalSalesManager:
                    return $@"AND GSMId in ({placeholder})";

                case PortalUserRole.NationalSalesManager:
                    return $@"AND NSMId in ({placeholder})";

                case PortalUserRole.ZonalSalesManager:
                    return $@"AND ZSMId in ({placeholder})";

                case PortalUserRole.RegionalSalesManager:
                    return $@"AND RSMId in ({placeholder})";

                case PortalUserRole.AreaSalesManager:
                    return $@"AND ASMId in ({placeholder})";

                case PortalUserRole.ClientEmployee:
                    return $@"AND ESMId in ({placeholder})";

                default:
                    return "";
            }
        }

        public static string EmployeeHierarchyOldIds(PortalUserRole userRole, long userId)
        {
            return $@"ISNULL(GSMId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.GlobalSalesManager}' THEN {userId}
                    ELSE  ISNULL(GSMId,0)
                    END
                    AND
                    ISNULL(NSMId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.NationalSalesManager}' THEN {userId}
                    ELSE  ISNULL(NSMId,0)
                    END
                    AND
                    ISNULL(ZSMId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.ZonalSalesManager}' THEN {userId}
                    ELSE ISNULL(ZSMId,0)
                    END
                    AND
                    ISNULL(RSMId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.RegionalSalesManager}' THEN {userId}
                    ELSE ISNULL(RSMId,0)
                    END
                    AND
                    ISNULL(ASMId,0) = CASE
                    WHEN '{userRole}'='{PortalUserRole.AreaSalesManager}' THEN {userId}
                    ELSE ISNULL(ASMId,0)
                    END
                    AND
                    ESMId = CASE
                    WHEN '{userRole}'='{PortalUserRole.ClientEmployee}' THEN {userId}
                    ELSE ESMId
                    END";
        }
    }

    public static class GeographicalHierarchyFilters
    {
        public static string GeographicalHierarchyTillRegion(GeographicalHierarchy geographicalHierarchy, long geoFilterId)
        {
            //return $@"ISNULL(Zone,0) = CASE
            //    WHEN '{geographicalHierarchy}'='{GeographicalHierarchy.zoneId}' THEN {geoFilterId}
            //    ELSE  ISNULL(Zone,0)
            //    END
            //    AND
            //    ISNULL(Region,0) = CASE
            //    WHEN '{geographicalHierarchy}'='{GeographicalHierarchy.regionId}' THEN {geoFilterId}
            //    ELSE  ISNULL(Region,0)
            //    END";
            return $@"
                    ISNULL(Region,0) = CASE
                    WHEN '{geographicalHierarchy}'='{GeographicalHierarchy.regionId}' THEN {geoFilterId}
                    ELSE  ISNULL(Region,0)
                    END";
        }

        public static string GeographicalIdsFilterTillRegion(GeographicalHierarchy geographicalHierarchy, string placeHolder = "$$Ids$$")
        {
            switch (geographicalHierarchy)
            {
                case GeographicalHierarchy.level7:
                    return $"AND L7GeoHierarchyId in ({placeHolder})";
                case GeographicalHierarchy.level6:
                    return $"AND L6GeoHierarchyId in ({placeHolder})";
                case GeographicalHierarchy.level5:
                    return $"AND L5GeoHierarchyId in ({placeHolder})";
                case GeographicalHierarchy.zoneId:
                    return $"AND ZoneId in ({placeHolder})";
                case GeographicalHierarchy.regionId:
                    return $"AND RegionId in ({placeHolder})";
                default:
                    return "";
            }
        }
    }

    public static class PositionHierarchyFilters
    {
        public static string PositionFilterForPositionIdsAndLevels(List<PositionCodeLevel> pcLevels, List<long> pcIds)
        {
            var query = "(";
            var i = 0;

            foreach (var l in pcLevels)
            {
                if (i > 0)
                {
                    query += "or";
                }

                if (l == PositionCodeLevel.L1Position)
                {
                    query += $"(isnull(PositionLevel1,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L2Position)
                {
                    query += $"(isnull(PositionLevel2,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L3Position)
                {
                    query += $"(isnull(PositionLevel3,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L4Position)
                {
                    query += $"(isnull(PositionLevel4,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L5Position)
                {
                    query += $"(isnull(PositionLevel5,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L6Position)
                {
                    query += $"(isnull(PositionLevel6,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L7Position)
                {
                    query += $"(isnull(PositionLevel7,-1) in ({string.Join(",", pcIds)}))";
                }

                if (l == PositionCodeLevel.L8Position)
                {
                    query += $"(isnull(PositionLevel8,-1) in ({string.Join(",", pcIds)}))";
                }

                i += 1;
            }

            if (pcLevels.Count == 0)
            {
                query += "1=0";
            }

            query += ")";
            return query;
        }

        public static string PositionFilterForPositionIdsAndLevel(IEnumerable<long> pcIds, PositionCodeLevel positionCodeLevel)
        {
            var res = "";
            switch (positionCodeLevel)
            {
                case PositionCodeLevel.L8Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel8 = {string.Join(",", pcIds)}" : $@"AND PositionLevel8 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L7Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel7 = {string.Join(",", pcIds)}" : $@"AND PositionLevel7 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L6Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel6 = {string.Join(",", pcIds)}" : $@"AND PositionLevel6 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L5Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel5 = {string.Join(",", pcIds)}" : $@"AND PositionLevel5 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L4Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel4 = {string.Join(",", pcIds)}" : $@"AND PositionLevel4 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L3Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel3 = {string.Join(",", pcIds)}" : $@"AND PositionLevel3 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L2Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel2 = {string.Join(",", pcIds)}" : $@"AND PositionLevel2 in ({string.Join(",", pcIds)})";
                    break;

                case PositionCodeLevel.L1Position:
                    res = pcIds.Count() == 1 ? $@"AND PositionLevel1 = {string.Join(",", pcIds)}" : $@"AND PositionLevel1 in ({string.Join(",", pcIds)})";
                    break;
            }

            return res;
        }

        public static string PositionFilterForPositionIdsAndLevelForLinq(IEnumerable<long> pcIds, PositionCodeLevel positionCodeLevel)
        {
            var res = "";
            switch (positionCodeLevel)
            {
                case PositionCodeLevel.L8Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel8 = @0" : @" PositionLevel8 in (@0)";
                    break;

                case PositionCodeLevel.L7Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel7 = @0" : @" PositionLevel7 in (@0)";
                    break;

                case PositionCodeLevel.L6Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel6 = @0" : @" PositionLevel6 in (@0)";
                    break;

                case PositionCodeLevel.L5Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel5 = @0" : @" PositionLevel5 in (@0)";
                    break;

                case PositionCodeLevel.L4Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel4 = @0" : @" PositionLevel4 in (@0)";
                    break;

                case PositionCodeLevel.L3Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel3 = @0" : @" PositionLevel3 in (@0)";
                    break;

                case PositionCodeLevel.L2Position:
                    res = pcIds.Count() == 1 ? @" PositionLevel2 = @0" : @"PositionLevel2 in (@0)";
                    break;

                case PositionCodeLevel.L1Position:
                    res = pcIds.Count() == 1 ? @"PositionLevel1 =  @0" : @"PositionLevel1 in (@0)";
                    break;
            }

            return res;
        }

        public static string PositionUserFilterForPositionUserIdsAndLevel(IEnumerable<long> pcUserIds, PositionCodeLevel positionCodeLevel)
        {
            var res = "";
            switch (positionCodeLevel)
            {
                case PositionCodeLevel.L8Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel8UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel8UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L7Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel7UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel7UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L6Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel6UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel6UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L5Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel5UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel5UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L4Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel4UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel4UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L3Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel3UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel3UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L2Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel2UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel2UserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L1Position:
                    res = pcUserIds.Count() == 1 ? $@"AND PositionLevel1UserId = {string.Join(",", pcUserIds)}" : $@"AND PositionLevel1UserId in ({string.Join(",", pcUserIds)})";
                    break;
            }

            return res;
        }

        public static string PositionUserFilterForPositionUserIdsAndLevelForUnifyDB(List<long> pcUserIds, PositionCodeLevel positionCodeLevel)
        {
            var res = "";
            switch (positionCodeLevel)
            {
                case PositionCodeLevel.L8Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level8PositionUserId = {pcUserIds[0]}" : $@"AND Level8PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L7Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level7PositionUserId = {pcUserIds[0]}" : $@"AND Level7PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L6Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level6PositionUserId = {pcUserIds[0]}" : $@"AND Level6PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L5Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level5PositionUserId = {pcUserIds[0]}" : $@"AND Level5PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L4Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level4PositionUserId = {pcUserIds[0]}" : $@"AND Level4PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L3Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level3PositionUserId = {pcUserIds[0]}" : $@"AND Level3PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L2Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level2PositionUserId = {pcUserIds[0]}" : $@"AND Level2PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;

                case PositionCodeLevel.L1Position:
                    res = pcUserIds.Count == 1 ? $@"AND Level1PositionUserId = {pcUserIds[0]}" : $@"AND Level1PositionUserId in ({string.Join(",", pcUserIds)})";
                    break;
            }

            return res;
        }

        public static string PositionUserFilterForPositionUserIdAndLevel(long pcUserId, PositionCodeLevel? pcUserLevel)
        {
            var res = "";
            switch (pcUserLevel)
            {
                case PositionCodeLevel.L8Position:
                    res = $@"AND PositionLevel8UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L7Position:
                    res = $@"AND PositionLevel7UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L6Position:
                    res = $@"AND PositionLevel6UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L5Position:
                    res = $@"AND PositionLevel5UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L4Position:
                    res = $@"AND PositionLevel4UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L3Position:
                    res = $@"AND PositionLevel3UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L2Position:
                    res = $@"AND PositionLevel2UserId = {pcUserId} ";
                    break;

                case PositionCodeLevel.L1Position:
                    res = $@"AND PositionLevel1UserId = {pcUserId} ";
                    break;
            }

            return res;
        }

        public static string ManagerNewIdsByPosInString(PositionCodeLevel? positionCodeLevelFilter, string placeholder = "$$Ids$$")
        {
            switch (positionCodeLevelFilter)
            {
                case PositionCodeLevel.L8Position:
                    return $@"AND PositionLevel8UserId in ({placeholder})";

                case PositionCodeLevel.L7Position:
                    return $@"AND PositionLevel7UserId in ({placeholder})";

                case PositionCodeLevel.L6Position:
                    return $@"AND PositionLevel6UserId in ({placeholder})";

                case PositionCodeLevel.L5Position:
                    return $@"AND PositionLevel5UserId in ({placeholder})";

                case PositionCodeLevel.L4Position:
                    return $@"AND PositionLevel4UserId in ({placeholder})";

                case PositionCodeLevel.L3Position:
                    return $@"AND PositionLevel3UserId in ({placeholder})";

                case PositionCodeLevel.L2Position:
                    return $@"AND PositionLevel2UserId in ({placeholder})";

                case PositionCodeLevel.L1Position:
                    return $@"AND PositionLevel1UserId in ({placeholder})";

                default: return "";
            }
        }

        public static string GetPositionIdFromLevel(PositionCodeLevel positionCodeLevel)
        {
            switch (positionCodeLevel)
            {
                case PositionCodeLevel.L8Position:
                    return @"PositionLevel8";

                case PositionCodeLevel.L7Position:
                    return @"PositionLevel7";

                case PositionCodeLevel.L6Position:
                    return @"PositionLevel6";

                case PositionCodeLevel.L5Position:
                    return @"PositionLevel5";

                case PositionCodeLevel.L4Position:
                    return @"PositionLevel4";

                case PositionCodeLevel.L3Position:
                    return @"PositionLevel3";

                case PositionCodeLevel.L2Position:
                    return @"PositionLevel2";

                default:
                    return "PositionLevel1";
            }
        }
    }

    public static class GeographyHierarchyFilters
    {
        public static string GeographyFilterForGeoIdsAndStrGeoType(List<long> geoIds, string geoType, bool forjoinCondition = false)
        {
            var query = "";
            if (forjoinCondition)
            {
                if (geoType == "Zone")
                {
                    return $" And e.ZoneId in ({string.Join(",", geoIds)})";
                }

                if (geoType == "Region")
                {
                    return $" And e.RegionId in ({string.Join(",", geoIds)})";
                }

                if (geoType == "Level5")
                {
                    return $" And e.GeographyLevel5 in ({string.Join(",", geoIds)})";
                }

                if (geoType == "Level6")
                {
                    return $" And e.GeographyLevel6 in ({string.Join(",", geoIds)})";
                }

                if (geoType == "Level7")
                {
                    return $" And e.GeographyLevel7 in ({string.Join(",", geoIds)})";
                }
            }

            return query;
        }

        /// <summary>
        /// Provides conditional query part for geographical filtering.
        /// </summary>
        /// <param name="geoIds">geoIds</param>
        /// <param name="geoType">geoType in string format</param>
        /// <returns> string format </returns>
        public static string GeographyFilterWithGeoIdsAndStrGeoType(List<long> geoIds, string geoType)
        {
            var query = "";
            if (geoType == "Zone")
            {
                return $" And ZoneId in ({string.Join(",", geoIds)})";
            }

            if (geoType == "zoneId")
            {
                return $" And ZoneId in ({string.Join(",", geoIds)})";
            }

            if (geoType == "Region")
            {
                return $" And RegionId in ({string.Join(",", geoIds)})";
            }

            if (geoType == "regionId")
            {
                return $" And RegionId in ({string.Join(",", geoIds)})";
            }

            if (geoType == "Level5")
            {
                return $" And GeographyLevel5 in ({string.Join(",", geoIds)})";
            }

            if (geoType == "Level6")
            {
                return $" And GeographyLevel6 in ({string.Join(",", geoIds)})";
            }

            if (geoType == "Level7")
            {
                return $" And GeographyLevel7 in ({string.Join(",", geoIds)})";
            }

            return query;
        }
    }

    public static class ProductHierarchyFilters
    {
        public static string ProductFilterForProdIdsAndProdType(List<long> prodIds, ProductHierarchy prodHierarchy)
        {
            var res = "";
            switch (prodHierarchy)
            {
                case ProductHierarchy.Division:
                    res = $@"AND ProductDivisionId in ({string.Join(", ", prodIds)})";
                    break;

                case ProductHierarchy.PrimaryCategory:
                    res = $@"AND PrimaryCategoryId in ({string.Join(", ", prodIds)})";
                    break;

                case ProductHierarchy.SecondaryCategory:
                    res = $@"AND SecondaryCategoryId in ({string.Join(", ", prodIds)})";
                    break;
                case ProductHierarchy.Product:
                    res = $@"AND ProductId in ({string.Join(", ", prodIds)})";
                    break;
            }

            return res;
        }
    }

    public static class Location
    {
        public static string FieldsForOutlets => @"l.erpid  'ShopErpId', l.ShopTypeId,
							l.shopname 'ShopName',  l.Address,	l.MarketName 'Market',l.SubCity,l.City, l.State,l.ShopType,l.PinCode Pincode, CAST(ISNULL(l.Segmentation,0) as nvarchar(5)) Segmentation,l.OwnersName , l.OwnersNo
                            ,iif(l.IsFocused=1,'Yes','No') ISFocused,iif(l.IsBlocked=1,'Yes','No') IsBlocked, l.GSTIN, l.PAN, l.Aadhar, l.Latitude, l.Longitude,l.FormattedAddress,l.CreatedAt,l.ImageId Image,
                            l.BankAccountNumber,  l.AccountHoldersName, l.PlaceOfDelivery, l.IFSCCode,CAST(ISNULL(l.OutletChannel,0) as nvarchar(10)) OutletChannel
                            ,l.AttributeText1
                            ,l.AttributeText2
                            ,l.AttributeText3
                            ,l.AttributeText4
                            ,l.AttributeNumber1
                            ,l.AttributeNumber2
                            ,l.AttributeNumber3
                            ,l.AttributeNumber4
                            ,l.AttributeBoolean1
                            ,l.AttributeBoolean2
                            ,l.AttributeDate1
                            ,l.AttributeDate2
                            ,l.AttributeImage1
                            ,l.AttributeImage2
                            ,l.AttributeImage3
                            ,l.AssetCode
                            ,iif(l.IsAssetPresent=1,'Yes','No') IsAssetPresent
                            ,l.AssetType
                            ,l.AssetSize
                            ,l.AssetDesc
                            ,l.Email
                            ,l.GeographicalMappingId";
    }
}
