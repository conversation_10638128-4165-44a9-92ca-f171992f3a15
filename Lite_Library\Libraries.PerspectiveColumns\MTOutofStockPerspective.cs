using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

internal sealed class MTOutofStockPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public MTOutofStockPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        new()
        {
            Attribute = "Field User",
            DisplayName = "National Sales Manager",
            Name = "NSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Zonal Sales Manager",
            Name = "ZSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Regional Sales Manager",
            Name = "RSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Area Sales Manager",
            Name = "ASM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Employee",
            Name = "ESM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Reporting Manager",
            Name = "ReportingManager",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Name",
            Name = "FieldUserName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "FieldUser HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User ERP ID",
            Name = "FieldUserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User Designation",
            Name = "UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User Date of Joining",
            Name = "UserDoJ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position}",
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position}",
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position}",
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position}",
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position}",
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position}",
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position}",
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position}",
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Reporting Manager",
            Name = "L2Position_ReportingUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser Name",
            Name = "L1Position_FieldUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Field User ERP ID",
            Name = "L1Position_UserERP",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Beat",
            Name = "Beat",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Territory",
            Name = "Territory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Zone",
            Name = "Zone",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Region",
            Name = "Region",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Level5",
            Name = "Level5",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Level6",
            Name = "Level6",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = "Level7",
            Name = "Level7",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets",
            Name = "Shop",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " ERPID",
            Name = "ShopERPID",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Owners Name",
            Name = "ShopOwnersName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Owners Number",
            Name = "ShopOwnersNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Address",
            Name = "ShopAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Market",
            Name = "ShopMarket",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Town",
            Name = "ShopSubCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " City",
            Name = "ShopCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " State",
            Name = "ShopState",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Type",
            Name = "ShopType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Segmentation",
            Name = "ShopSegmentation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Outlets" + " Channel",
            Name = "ShopChannel",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Outlets",
            DisplayName = "Chain Name",
            Name = "ChainName",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "Product",
            Name = "ProductName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "SecondaryCategory",
            Name = "SecondaryCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new()
        {
            Attribute = "Product",
            DisplayName = "PrimaryCategory",
            Name = "PrimaryCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Date",
            Name = "Date",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Time",
            Name = "Time",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            NotForReports = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "OoS SKUs",
            Name = "OoSSKUs",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "Total SKUs",
            Name = "TotalSKUs",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "Total Not Listed SKUs",
            Name = "TotalNotListedSKUs",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "Total Not Linked SKUs",
            Name = "TotalNotLinkedSKUs",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "Available Facings",
            Name = "AvailableFacings",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "Available Stocks",
            Name = "AvailableStocks",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Campaign Measure",
            DisplayName = "OoS%",
            Name = "OoSSKUs_TotalSKUs",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Avg
        },
        new() { Attribute = "Campaign Pivot Measures", DisplayName = "Availability", Name = "_A_Availability", PerspectiveMeasure = PerspectiveMeasure.CountDistinct }
    };
}
