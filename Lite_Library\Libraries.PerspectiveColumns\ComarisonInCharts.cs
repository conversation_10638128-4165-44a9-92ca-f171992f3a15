﻿using System.Collections.Generic;
using System.Linq;
using Libraries.CommonEnums;

namespace Libraries.PerspectiveColumns;

public static class ComarisonInCharts
{
    public static List<ComparisonColumnModel> Columns => new()
    {
        new ComparisonColumnModel { ComparisonTimePeriod = ComparisonTimePeriod.Month, DateRangePreset = DateRangePreset.MTD, ComparisonType = ComparisonType.LastMonth },
        new ComparisonColumnModel { ComparisonTimePeriod = ComparisonTimePeriod.Week, DateRangePreset = DateRangePreset.Yesterday, ComparisonType = ComparisonType.LastWeek },
        new ComparisonColumnModel { ComparisonTimePeriod = ComparisonTimePeriod.Week, DateRangePreset = DateRangePreset.Yesterday, ComparisonType = ComparisonType.LastMonthCurrentWeek },
        new ComparisonColumnModel { ComparisonTimePeriod = ComparisonTimePeriod.Day, DateRangePreset = DateRangePreset.Yesterday, ComparisonType = ComparisonType.Yesterday },
        new ComparisonColumnModel { ComparisonTimePeriod = ComparisonTimePeriod.Day, DateRangePreset = DateRangePreset.Yesterday, ComparisonType = ComparisonType.LastMonthCurrentDay }
        //new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Day, DateRangePreset = DateRangePreset.Yesterday, ComparisonType= ComparisonType.ThisMonthAverage},
    };

    public static List<ComparisonColumnModel> GetComparisonColumns(DateRangePreset dateRangePreset)
    {
        return Columns.Where(s => s.DateRangePreset == dateRangePreset).ToList();
    }

    public class ComparisonColumnModel
    {
        public ComparisonTimePeriod ComparisonTimePeriod { get; set; }
        public ComparisonType ComparisonType { get; set; }
        public DateRangePreset DateRangePreset { get; set; }
    }
}
