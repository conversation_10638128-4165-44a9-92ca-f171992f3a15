﻿using System;
using Microsoft.Extensions.Configuration;

namespace Library.CommonHelpers.Extensions;

public static class ConfigurationExtensions
{
    public static string GetRequiredConnectionString(this IConfiguration configuration, string connectionStringName)
    {
        var connectionString = configuration.GetConnectionString(connectionStringName);

        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException($"Connection string: '{connectionStringName}' not found.");
        }

        return connectionString;
    }
}
