﻿using Microsoft.EntityFrameworkCore;
using RouteOptimizationProcessor.Core.Models.DbModels;
using RouteOptimizationProcessor.Core.Repositories;
using RouteOptimizationProcessor.DbStorage.DbContexts;

namespace RouteOptimizationProcessor.DbStorage.Repositories;

public class RoutePlanAutomationRepository(MasterDbContext masterDbContext) : IRoutePlanAutomationRepository
{
    public async Task<RouteAutomationConfiguration?> GetSingleAsync(long id, CancellationToken ct = default)
    {
        return await masterDbContext.RouteAutomationConfigurations.FindAsync(id, ct);
    }
}
