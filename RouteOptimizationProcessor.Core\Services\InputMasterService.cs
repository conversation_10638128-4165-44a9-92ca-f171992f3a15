﻿using ClosedXML.Excel;
using Libraries.CommonEnums;
using Library.StorageWriter.Reader_Writer;
using RouteOptimizationProcessor.Core.Helpers;
using RouteOptimizationProcessor.Core.Models.CoreModels;
using RouteOptimizationProcessor.Core.Models.QueueModels;
using RouteOptimizationProcessor.Core.Repositories;

namespace RouteOptimizationProcessor.Core.Services;

public interface IInputMasterService
{
    Task ProcessQueueAsync(RoutePlaygroundQueueModel queueData);
}

public class InputMasterService(IRoutePlanAutomationRepository routePlanAutomationRepository,
    ICohortService cohortService,
    IEmployeeRepository employeeRepository,
    ILocationService locationService,
    ILocationRepository locationRepository,
    FaiDataLakeBlobWriter faiDataLakeBlobWriter, 
    RoFileSplitQueueHandler roFileSplitQueueHandler) : IInputMasterService
{
    public async Task ProcessQueueAsync(RoutePlaygroundQueueModel queueData)
    {
        var config = await routePlanAutomationRepository.GetSingleAsync(queueData.Id);

        if (config == null || config.InputType == InputType.Manual || config.CohortId == null)
        {
            return;
        }
        
        var employeeIds = await cohortService.GetEmployeesFromCohortAsync(config.CompanyId, config.CohortId.Value); 
        if (employeeIds == null || employeeIds.Count == 0)
        {
            return;
        }

        var employeeErpDictionary = await employeeRepository.GetEmployeeErpIdDictionaryAsync(config.CompanyId, employeeIds);

        var defaultVisitDefinitionData = config.VisitDefinitionJsonList?.FirstOrDefault(s => s.Id == 0);

        List<InputMasterExcelColumns> finalInputMaster = [];

        Func<Models.CoreModels.Location, (double requiredVisits, double retailTime)> visitStrategy = outlet => (0, 0);

        switch (config.VisitDefinitionType)
        {
            case VisitDefinitionType.Segmentation:
                var segmentationIds = config.VisitDefinitionJsonList?.Select(d => d.Id).ToList() ?? [];
                var outletSegementationsDict = await locationRepository.GetSegementionEnumsForIds(segmentationIds, config.CompanyId); // enum, list[id]
                visitStrategy = outlet =>
                {
                    if (outletSegementationsDict.TryGetValue(outlet.Segmentation, out var segIds))
                    {
                        var match = config.VisitDefinitionJsonList?.FirstOrDefault(d => segIds.Contains(d.Id));
                        return ((double requiredVisits, double retailTime))(match != null ? (match.RequiredVisits, match.RetailTime) : (0.0, 0.0));
                    }
                    return (0.0, 0.0);
                };
                break;

            case VisitDefinitionType.ShopType:
                var shopTypeIds = config.VisitDefinitionJsonList?.Select(d => d.Id).ToList() ?? [];

                visitStrategy = outlet =>
                {
                    var match = config.VisitDefinitionJsonList?.FirstOrDefault(d => d.Id == outlet.ShopTypeId);
                    return ((double requiredVisits, double retailTime))(match != null ? (match.RequiredVisits, match.RetailTime) : (0.0, 0.0)); 
                };
                break;

            case VisitDefinitionType.Channel:
                var channelIds = config.VisitDefinitionJsonList?.Select(d => d.Id).ToList() ?? [];
                visitStrategy = outlet =>
                {
                    var match = config.VisitDefinitionJsonList?.FirstOrDefault(d => d.Id == outlet.ChannelId);
                    return ((double requiredVisits, double retailTime))(match != null ? (match.RequiredVisits, match.RetailTime) : (0.0, 0.0));
                };
                break;
        }

        foreach (var employeeId in employeeIds)
        {
            var outletUniverse = await locationService.GetLocationsForEmployeesAsync(config.CompanyId, employeeId);
            if (outletUniverse == null || outletUniverse.Count == 0)
            {
                continue;
            }

            List<InputMasterExcelColumns> employeeData = [];

            foreach (var outlet in outletUniverse)
            {
                var outletData = new InputMasterExcelColumns
                {
                    EmployeeId = employeeErpDictionary[employeeId],
                    OutletId = outlet.ErpId,
                    ShopName = outlet.ShopName,
                    Latitude = outlet.Latitude ?? 0,
                    Longitude = outlet.Longitude ?? 0
                };

                var (requiredVisits, retailTime) = visitStrategy(outlet);

                outletData.RequiredVisits = requiredVisits > 0 ? requiredVisits : defaultVisitDefinitionData?.RequiredVisits ?? 0;
                outletData.RetailTime = retailTime > 0 ? retailTime : defaultVisitDefinitionData?.RetailTime ?? 0;

                employeeData.Add(outletData);
            }

            finalInputMaster.AddRange(employeeData);
        }

        if (finalInputMaster.Count > 0)
        {
            var fileName = $"{queueData.Id}/{queueData.FileName}";
            using var stream = new MemoryStream();
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("InputMaster");
                worksheet.Cell(1, 1).InsertTable(finalInputMaster);
                workbook.SaveAs(stream);
            }

            stream.Position = 0;

            await faiDataLakeBlobWriter.UploadStreamAsync(
                "ro-playground",
                stream,
                fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            );
        }

        // Trigger the next queue
        await roFileSplitQueueHandler.AddToQueue(queueData);
    }
}