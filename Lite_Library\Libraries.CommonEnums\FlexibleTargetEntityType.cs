﻿namespace Libraries.CommonEnums;

public enum FlexibleTargetEntityType
{
    Position = 1,
    User = 2,
    Beat = 3,
    Outlet = 4,
    Territory = 5,
    Region = 6,
    Zone = 7,
    Product = 8,
    PrimaryCategory = 9,
    SecondaryCategory = 10,
    ProductDivision = 11,
    DisplayCategory = 12,
    Distributor = 13,
    OutletChannel = 14,
    OutletSegmentation = 15,
    ShopTypes = 16,
    DistributorChannel = 17,
    DistributorSegmentation = 18,
    FocusedProductRule = 19,
    MustSellRule = 20,
    Level5Geography = 21,
    Level6Geography = 22,
    Level7Geography = 23,
    Route = 24
}

public enum FlexibleTargetFrequency
{
    Monthly = 1,
    Weekly = 2,
    Daily = 3,
    Custom = 4
}
