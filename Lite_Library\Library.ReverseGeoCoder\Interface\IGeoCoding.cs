﻿namespace Library.ReverseGeoCoder.Interface;

public interface IGeoCoding
{
    //GeoCodedReturnData GetDataFromLatLng(decimal lat, decimal lng);
    GeoCodedReturnData GetDataFromLatLongFromGoogle(decimal lat, decimal lng, bool isoldAPI = false);

    GeoCodedReturnData GetDataFromLatLngFromMapMyIndia(decimal lat, decimal lng);

    GeoCodedReturnData GetDataFromLatLngFromLocationIQ(decimal lat, decimal lng);
}
