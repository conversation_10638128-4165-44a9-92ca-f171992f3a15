﻿using System;

namespace Library.ConnectionStringParsor;

public class ApiConnectionString : ConnectionStringBase
{
    private ApiConnectionString(string connectionString) : base(connectionString)
    {
        var parsedString = Parse();
        if (parsedString.TryGetValue("baseurl", out var value))
        {
            BaseUrl = value;
        }
        else
        {
            throw new Exception("MalFormed Connection String: 'accountendpoint' is compulsory");
        }

        if (parsedString.TryGetValue("authtoken", out var value1))
        {
            AuthToken = value1;
        }
        else
        {
            throw new Exception("MalFormed Connection String: 'authtoken' is compulsory");
        }
    }

    public string AuthToken { get; }
    public string BaseUrl { get; }

    public static ApiConnectionString GetConnection(string connString)
    {
        return new ApiConnectionString(connString);
    }
}
