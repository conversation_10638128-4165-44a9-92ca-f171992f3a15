﻿using System.Collections.Generic;
using Libraries.CommonEnums;

namespace Libraries.CommonModels;

public class OutletMarginSlabMin : EntityMin
{
    public MarginEntity EntityType { get; set; }
    public bool Deleted { get; set; }
}

public class EntityMarginSlabMinModel : EntityMin
{
    public double Value { get; set; }
}

public class OutletMarginSlabModel : EntityMin
{
    public MarginEntity EntityType { get; set; }
    public List<EntityMarginSlabMinModel> EntityMargin { get; set; }
}
