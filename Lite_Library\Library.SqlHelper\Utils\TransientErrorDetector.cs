﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace Library.SqlHelper.Utils;

public class TransientErrorDetector
{
    //https://learn.microsoft.com/en-us/azure/azure-sql/database/troubleshoot-common-errors-issues?view=azuresql and Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTransientExceptionDetector
    private static readonly HashSet<int> SQLTransientErrorCodes = new()
    {
        20,
        64,
        121,
        233,
        1205,
        10053,
        10054,
        10060,
        10928,
        10929,
        40197,
        40501,
        40613,
        41301,
        41302,
        41305,
        41325,
        41839,
        49918,
        49919,
        49920,
        926,
        4060,
        4221,
        615,
        18456
    };

    private static readonly List<string> TransientMessagesInOurSystem = new()
    {
        "try again",
        "error occurred during the login process.",
        "A severe error occurred on the current command",
        "A transport-level error",
        "Login failed",
        "An exception has been raised that is likely due to a transient failure",
        "The request limit for the database",
        "Timeout expired",
        "The service has encountered an error processing your request",
        "an error occurred during the pre-login handshake",
        "specified block list is invalid",
        "The transaction was terminated because of the availability replica config/state change"
    };

    public static bool ShouldRetryOn(Exception ex)
    {
        if (ex is SqlException ex2)
        {
            {
                var enumerator = ex2.Errors.GetEnumerator();
                try
                {
                    while (enumerator.MoveNext())
                    {
                        if (SQLTransientErrorCodes.Contains(((SqlError)enumerator.Current).Number))
                        {
                            return true;
                        }
                    }
                }
                finally
                {
                    if (enumerator is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }
            }
        }

        if (ex is TimeoutException)
        {
            return true;
        }

        return TransientMessagesInOurSystem.Any(s => ex.Message.Contains(s));
    }
}
