﻿using EntityHelper;
using System.ComponentModel.DataAnnotations.Schema;

namespace RouteOptimizationProcessor.Core.Models.DbModels;

public class EmployeeRouteMapping : IAuditedEntity
{
    public long Id { get; set; }

    public long EmployeeId { get; set; }

    public long RouteId { get; set; }

    public DateTime TimeAdded { get; set; }

    public long CompanyId { get; set; }

    public string CreationContext { get; set; }

    public bool IsDeleted { get; set; }
    public bool IsTemporaryAttachment { get; set; }

    public ClientEmployee Employee { get; set; }

    public Route Route { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }
}

[Table("RoutePositionMappings")]
public class RoutePositionMappings
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public bool Deleted { get; set; }
    public long RouteId { get; set; }
    public virtual Route Routes { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public long PositionCodeId { get; set; }
}