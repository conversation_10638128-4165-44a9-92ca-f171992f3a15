﻿namespace Libraries.CommonEnums;

/// <summary>
///     Represents the status of an attendance order. (related table OrderStatus in transaction db)
/// </summary>
public enum AttendanceOrderStatus
{
    /// <summary>
    ///     The order is pending.
    /// </summary>
    PendingOrder = 0,

    /// <summary>
    ///     The order has been accepted.
    /// </summary>
    Accepted = 1,

    /// <summary>
    ///     The order has been rejected.
    /// </summary>
    Rejected = 2,

    /// <summary>
    ///     The order has been automatically closed.
    /// </summary>
    AutoClosed = 3,

    /// <summary>
    ///     The order has been cancelled.
    /// </summary>
    Cancelled = 4,

    /// <summary>
    ///     The order has been invoiced.
    /// </summary>
    Invoiced = 5,

    /// <summary>
    ///     The order is partially completed.
    /// </summary>
    Partial = 6,

    /// <summary>
    /// The order is pending for Cancellation.
    /// </summary>
    PendingForCancellation = 7
}
