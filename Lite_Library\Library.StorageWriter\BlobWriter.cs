﻿using System;
using System.IO;
using System.Reflection.Metadata;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Http;

namespace Library.StorageWriter;

public class BlobWriter : BlobReader
{
    public BlobWriter(string storageConnectionString, string containerName) : base(storageConnectionString,
        containerName)
    {
    }

    public async Task<string> AppendToBlob<T>(string filename, T request)
    {
        var blobClient = _container.GetBlobClient(filename);

        if (await blobClient.ExistsAsync().ConfigureAwait(false))
        {
            var oldContent = await blobClient.DownloadAsync().ConfigureAwait(false);
            var newContent = oldContent + "\n\n" + JsonSerializer.Serialize(request);
            await blobClient.UploadAsync(newContent, true).ConfigureAwait(false);
            return GetPublicPath(filename);
        }

        var binaryData = BinaryData.FromString(JsonSerializer.Serialize(request, request.GetType(), new JsonSerializerOptions { WriteIndented = true }));
        await blobClient.UploadAsync(binaryData, true).ConfigureAwait(false);
        return GetPublicPath(filename);
    }

    //public async Task<DateTime?> DeleteIfOlder(string fileId, DateTime oldestAllowedTransactionBlobdate)
    //{
    //    BlobClient blobClient = this._container.GetBlobClient(fileId);
    //    await blobClient.FetchAttributesAsync();
    //    if (blobClient.Properties.LastModified < oldestAllowedTransactionBlobdate)
    //    {
    //        await blobClient.DeleteAsync();
    //        return blobClient.Properties.LastModified?.DateTime;
    //    }
    //    return null;
    //}

    public async Task<Stream> GetWriteStream(string filename, string contentType = "application/octet-stream")
    {
        var blobClient = _container.GetBlobClient(filename);
        //src: https://github.com/Azure/azure-sdk-for-net/issues/16875
        var options = new BlobOpenWriteOptions { HttpHeaders = new BlobHttpHeaders { ContentType = contentType }, OpenConditions = new BlobRequestConditions { IfNoneMatch = ETag.All } };

        //src: https://stackoverflow.com/a/65395088/11519765
        return await blobClient.OpenWriteAsync(true, options).ConfigureAwait(false);
    }

    public async Task<bool> IsBlobOlder(string fileId, DateTime oldestAllowedTransactionBlobdate)
    {
        var blobClient = _container.GetBlobClient(fileId);
        await blobClient.GetPropertiesAsync().ConfigureAwait(false);
        //src: https://stackoverflow.com/a/67697507/11519765
        return (await blobClient.GetPropertiesAsync().ConfigureAwait(false)).Value.LastModified < oldestAllowedTransactionBlobdate;
    }

    /// <summary>
    ///     Uploads a blob asynchronously to Azure Blob Storage.
    /// </summary>
    /// <param name="blobName">The name of the blob to upload.</param>
    /// <param name="stream">The stream containing the data to upload.</param>
    /// <returns>A Task representing the asynchronous operation. The result is the BlobClient representing the uploaded blob.</returns>
    public async Task<BlobClient> UploadBlobAsync(string blobName, Stream stream)
    {
        var blob = _container.GetBlobClient(blobName);
        await blob.UploadAsync(stream, true).ConfigureAwait(false);
        return blob;
    }

    /// <summary>
    ///     Deletes a blob asynchronously from Azure Blob Storage if it exists.
    /// </summary>
    /// <param name="blobName">The name of the blob to delete.</param>
    /// <returns>A Task representing the asynchronous operation.</returns>
    public async Task DeleteBlobAsync(string blobName)
    {
        var blob = _container.GetBlobClient(blobName);
        await blob.DeleteIfExistsAsync().ConfigureAwait(false);
    }

    public async Task MoveToStream(string sourceBlob, Stream destBlob)
    {
        var blobClient = _container.GetBlobClient(sourceBlob);
        if (await blobClient.ExistsAsync().ConfigureAwait(false))
        {
            using (var srcStream = await blobClient.OpenReadAsync().ConfigureAwait(false))
            {
                await srcStream.CopyToAsync(destBlob).ConfigureAwait(false);
                destBlob.Close();
                srcStream.Close();
                await blobClient.DeleteIfExistsAsync().ConfigureAwait(false);
            }
        }
    }

    public async Task Rename(string filePath, string newName)
    {
        var blob = _container.GetBlobClient(filePath);
        var blobCopy = _container.GetBlobClient(newName);
        if (await blob.ExistsAsync().ConfigureAwait(false))
        {
            //src: https://stackoverflow.com/a/65820001/11519765
            await (await blobCopy.StartCopyFromUriAsync(blob.Uri).ConfigureAwait(false)).WaitForCompletionAsync().ConfigureAwait(false);
            await blob.DeleteIfExistsAsync().ConfigureAwait(false);
        }
    }

    public async Task<string> WriteToBlob<T>(string filename, T request, bool useUnsafeJson = false)
    {
        var blobClient = _container.GetBlobClient(filename);
        /*
         * Polymorphic Serialization not implemented in System.Text.Json, should work by default in .net 7
         * https://stackoverflow.com/a/64791906/11519765
         * https://learn.microsoft.com/en-us/dotnet/standard/serialization/system-text-json/polymorphism?pivots=dotnet-6-0
         */
        var jsonSerializerOptions = new JsonSerializerOptions { WriteIndented = true };
        if (useUnsafeJson)
        {
            jsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        }

        var binaryData = BinaryData.FromString(JsonSerializer.Serialize(request, request.GetType(), jsonSerializerOptions));
        //UploadAsync does not accept string as Input, instead only Binary and Stream
        await blobClient.UploadAsync(binaryData, true).ConfigureAwait(false);
        return GetPublicPath(filename);
    }

    public async Task<string> WriteToBlob(string filename, string data, string contentType)
    {
        var blobClient = _container.GetBlobClient(filename);
        await blobClient.UploadAsync(data, new BlobHttpHeaders { ContentType = contentType }).ConfigureAwait(false);
        return GetPublicPath(filename);
    }

    public async Task<string> WriteToBlob_Temp<T>(string filename, T request)
    {
        var blobClient = _container.GetBlobClient(filename);
        var requestTemp = "temp";
        try
        {
            requestTemp = JsonSerializer.Serialize(request);
            //throw new Exception("test exception");
        }
        catch (Exception ex)
        {
            try
            {
                using (var stream = await GetWriteStream($"Exceptions/{filename}.json").ConfigureAwait(false))
                {
                    await JsonSerializer.SerializeAsync(stream, request, new JsonSerializerOptions { ReferenceHandler = ReferenceHandler.IgnoreCycles, DefaultIgnoreCondition = JsonIgnoreCondition.Never }).ConfigureAwait(false);
                }

                using (var exStream = await GetWriteStream($"Exceptions/{filename}.txt").ConfigureAwait(false))
                using (TextWriter textWriter = new StreamWriter(exStream))
                {
                    await textWriter.WriteAsync(ex.ToString()).ConfigureAwait(false);
                }
            }
            catch (Exception)
            {
            }
        }

        await blobClient.UploadAsync(requestTemp).ConfigureAwait(false);
        return GetPublicPath(filename + ".json");
    }

    public async Task UploadByteArrayAsync(
        byte[] fileBytes,
        string name,
        string contentType)
    {
        var blobClient = _container.GetBlobClient(name);
        var blobUploadOption = new BlobUploadOptions { HttpHeaders = new BlobHttpHeaders { ContentType = contentType } };
        await blobClient.UploadAsync(BinaryData.FromBytes(fileBytes), blobUploadOption).ConfigureAwait(false);
    }

    public async Task UploadStreamAsync(
        Stream stream,
        string name,
        string contentType)
    {
        stream.Seek(0, SeekOrigin.Begin);
        var blob = _container.GetBlobClient(name);
        await blob.UploadAsync(stream, new BlobHttpHeaders { ContentType = contentType }).ConfigureAwait(false);
    }

    public async Task UploadFileAsync(
        IFormFile file,
        string name,
        string contentType)
    {
        var headers = new BlobHttpHeaders { ContentType = contentType };
        var blobClient = _container.GetBlobClient(name);
        using (var stream = file.OpenReadStream())
        {
            await blobClient.UploadAsync(stream, headers).ConfigureAwait(false);
        }
    }
}

public class BlobWriterUnknown : BlobReaderUnknown
{
    public BlobWriterUnknown(string storageConnectionString) : base(storageConnectionString)
    {
    }

    public async Task UploadFileAsync(string containerName,
        IFormFile file,
        string name,
        string contentType)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var headers = new BlobHttpHeaders { ContentType = contentType };
        var blobClient = container.GetBlobClient(name);
        using (var stream = file.OpenReadStream())
        {
            await blobClient.UploadAsync(stream, headers).ConfigureAwait(false);
        }
    }

    public async Task UploadByteArrayAsync(string containerName,
        byte[] fileBytes,
        string name,
        string contentType)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var blobClient = container.GetBlobClient(name);
        var blobUploadOption = new BlobUploadOptions { HttpHeaders = new BlobHttpHeaders { ContentType = contentType } };
        await blobClient.UploadAsync(BinaryData.FromBytes(fileBytes), blobUploadOption).ConfigureAwait(false);
    }

    public async Task UploadStreamAsync(string containerName,
        Stream stream,
        string name,
        string contentType)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var blobClient = container.GetBlobClient(name);
        stream.Position = 0;
        await blobClient.UploadAsync(stream, new BlobHttpHeaders { ContentType = contentType }).ConfigureAwait(false);
    }

    public async Task DeleteBlobAsync(string containerName, string blobName, CancellationToken ct)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var blobClient = container.GetBlobClient(blobName);
        await blobClient.DeleteIfExistsAsync(cancellationToken: ct).ConfigureAwait(false);
    }
}
