﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum NotificationCondition
{
    General = 10,
    KRABased = 20
}

public enum CalculationType
{
    App = 10,
    Backend = 20
}

public enum AppNotificationType
{
    Push = 10,
    InApp = 20
}

public enum Periodicity
{
    Daily,
    Weekly,
    Monthly,
    Annually
}

public enum ComparisonOperator
{
    [Display(Name = "Less Than")]
    Less<PERSON>han,

    [Display(Name = "Less Than Or Equal To")]
    LessThanOrEqualTo,

    [Display(Name = "Equal To")]
    EqualTo,

    [Display(Name = "Greater Than Or Equal To")]
    GreaterThanOrEqualTo,

    [Display(Name = "Greater Than")]
    GreaterThan,

    [Display(Name = "Not Equal To")]
    NotEqualTo
}

public enum UserPlatform
{
    AnalyticApp = 10,
    ModMartApp = 20,
    UserApp = 30,
    FATradeApp = 40,
    AnalyticAppMT = 100
}
