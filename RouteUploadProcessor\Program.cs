﻿// See htusing Azure.Identity;
using Azure.Identity;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;

var builder = new HostBuilder()
    .ConfigureAppConfiguration((config) =>
    {
        var env = Environment.GetEnvironmentVariable("BuildEnvironment");
        var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
        if (!string.IsNullOrEmpty(keyVaultEndpoint))
        {
            config.AddAzureKeyVault(new Uri(keyVaultEndpoint), new DefaultAzureCredential());
        }
    })
    .ConfigureWebHostDefaults(webBuilder =>
    {
        webBuilder.Configure(app =>
        {
            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapGet("/", async context =>
                {
                    await context.Response.WriteAsync("Hello, <PERSON>!");
                });
            });
        });
        // Add the following line:
        // TODO: Will change sentry later with correct dns
        //webBuilder.UseSentry(o =>
        //{
        //    o.Dsn = "http://<EMAIL>:8000/5";
        //    o.Debug = false; // Disable Sentry debugging logs
        //    o.TracesSampleRate = 1.0; // Capture 100% of transactions
        //    o.Environment = Environment.GetEnvironmentVariable("BuildEnvironment") ?? "Production";
        //});
    })
    .ConfigureWebJobs(b =>
    {
        b.AddAzureStorageQueues(c =>
        {
            c.BatchSize =
#if DEBUG
                1;
#else
                                1;
#endif
            c.MaxPollingInterval = TimeSpan.FromSeconds(8);
        });
    })
    .ConfigureServices((context, services) =>
    {
        Dependencies.SetUp(context.Configuration, services);
    })
    .UseConsoleLifetime();
var host = builder.Build();
using (host)
{
    try
    {
        await host.RunAsync();
    }
    catch (Exception ex)
    {
        SentrySdk.CaptureException(ex);
        throw;
    }
}